#!/usr/bin/env python3
"""
测试程序09: 测试修复后的API
验证所有API端点都能正常工作
"""

import requests
import json
import time
from datetime import datetime

def test_all_apis():
    """测试所有API端点"""
    base_url = "http://localhost:8080"
    
    apis = [
        {
            'name': '连板进度数据',
            'endpoint': '/api/lianban_progress',
            'timeout': 10
        },
        {
            'name': '市场概况数据', 
            'endpoint': '/api/market_summary',
            'timeout': 10
        },
        {
            'name': '涨跌停统计数据',
            'endpoint': '/api/limit_stats',
            'timeout': 10
        },
        {
            'name': '实时连板进度数据',
            'endpoint': '/api/realtime/lianban_progress',
            'timeout': 15
        },
        {
            'name': '实时市场概况数据',
            'endpoint': '/api/realtime/market_summary',
            'timeout': 15
        },
        {
            'name': '实时市场历史数据',
            'endpoint': '/api/realtime/history/market',
            'timeout': 10
        },
        {
            'name': '实时连板历史数据',
            'endpoint': '/api/realtime/history/lianban',
            'timeout': 10
        }
    ]
    
    print("🔍 测试所有API端点...")
    print("=" * 80)
    
    success_count = 0
    total_count = len(apis)
    
    for api in apis:
        try:
            url = f"{base_url}{api['endpoint']}"
            print(f"\n📡 测试: {api['name']}")
            print(f"   端点: {api['endpoint']}")
            
            start_time = time.time()
            response = requests.get(url, timeout=api['timeout'])
            end_time = time.time()
            
            duration = end_time - start_time
            print(f"   状态码: {response.status_code} ({duration:.2f}s)")
            
            if response.status_code == 200:
                data = response.json()
                
                if isinstance(data, dict) and data.get('success'):
                    print(f"   ✅ 成功")
                    
                    if 'data' in data:
                        api_data = data['data']
                        if isinstance(api_data, list):
                            print(f"   数据条数: {len(api_data)}")
                        elif isinstance(api_data, dict):
                            print(f"   数据字段: {list(api_data.keys())}")
                    
                    success_count += 1
                else:
                    print(f"   ⚠️ API返回失败: {data.get('error', '未知错误')}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                
        except requests.exceptions.Timeout:
            print(f"   ❌ 超时 (>{api['timeout']}s)")
        except Exception as e:
            print(f"   ❌ 异常: {e}")
    
    print(f"\n📊 测试结果: {success_count}/{total_count} 个API正常工作")
    return success_count == total_count

def test_frontend_data_loading():
    """测试前端数据加载"""
    print("\n\n🌐 测试前端数据加载...")
    print("=" * 80)
    
    try:
        # 测试主页面
        response = requests.get("http://localhost:8080/", timeout=10)
        if response.status_code == 200:
            print("   ✅ 主页面可访问")
            
            # 检查页面内容
            content = response.text
            if 'loadLianbanData' in content:
                print("   ✅ 包含连板数据加载函数")
            if 'loadMarketData' in content:
                print("   ✅ 包含市场数据加载函数")
            if 'loadRealtimeHistory' in content:
                print("   ✅ 包含实时数据历史加载函数")
        else:
            print(f"   ❌ 主页面访问失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 前端测试异常: {e}")

def test_data_consistency():
    """测试数据一致性"""
    print("\n\n📊 测试数据一致性...")
    print("=" * 80)
    
    try:
        # 测试实时数据历史
        market_response = requests.get("http://localhost:8080/api/realtime/history/market", timeout=10)
        lianban_response = requests.get("http://localhost:8080/api/realtime/history/lianban", timeout=10)
        
        if market_response.status_code == 200 and lianban_response.status_code == 200:
            market_data = market_response.json()
            lianban_data = lianban_response.json()
            
            if market_data.get('success') and lianban_data.get('success'):
                market_count = len(market_data.get('data', []))
                lianban_count = len(lianban_data.get('data', []))
                
                print(f"   市场实时数据: {market_count} 条")
                print(f"   连板实时数据: {lianban_count} 条")
                
                if market_count > 0 and lianban_count > 0:
                    print("   ✅ 实时数据历史正常")
                    
                    # 检查时间范围
                    market_times = [item.get('time') for item in market_data['data'] if item.get('time')]
                    lianban_times = [item.get('time') for item in lianban_data['data'] if item.get('time')]
                    
                    if market_times and lianban_times:
                        print(f"   市场数据时间范围: {min(market_times)} - {max(market_times)}")
                        print(f"   连板数据时间范围: {min(lianban_times)} - {max(lianban_times)}")
                        
                        # 检查是否有9:30之前的数据
                        early_market = [t for t in market_times if t < "09:30"]
                        early_lianban = [t for t in lianban_times if t < "09:30"]
                        
                        if not early_market and not early_lianban:
                            print("   ✅ 时间过滤正确，无9:30之前的数据")
                        else:
                            print(f"   ⚠️ 发现早期数据: 市场{len(early_market)}条, 连板{len(early_lianban)}条")
                else:
                    print("   ⚠️ 实时数据历史为空")
            else:
                print("   ❌ 实时数据历史API返回失败")
        else:
            print("   ❌ 实时数据历史API访问失败")
            
    except Exception as e:
        print(f"   ❌ 数据一致性测试异常: {e}")

def main():
    """主函数"""
    print("🚀 开始测试修复后的API")
    print("=" * 80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 等待服务器启动
    print("\n⏳ 等待服务器启动...")
    time.sleep(2)
    
    # 执行测试
    all_apis_ok = test_all_apis()
    test_frontend_data_loading()
    test_data_consistency()
    
    print("\n\n✅ API修复测试完成!")
    print("=" * 80)
    
    if all_apis_ok:
        print("🎉 所有API都正常工作！")
        print("🌐 现在可以刷新浏览器页面查看修复效果")
    else:
        print("⚠️ 部分API仍有问题，需要进一步检查")

if __name__ == "__main__":
    main()
