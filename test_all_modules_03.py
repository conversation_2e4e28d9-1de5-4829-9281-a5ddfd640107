#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试程序03：验证所有四个趋势模块的时间过滤效果
测试连板趋势图、市场概况趋势图、涨跌停趋势图、成交金额趋势图
"""

import os
import sys
import json
import requests
from datetime import datetime, time

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def is_market_open_time(time_str):
    """判断时间是否在开盘时间（9:30-11:30, 13:00-15:00）"""
    if not time_str:
        return False
    
    try:
        time_parts = time_str.split(':')
        if len(time_parts) != 2:
            return False
        
        hour = int(time_parts[0])
        minute = int(time_parts[1])
        total_minutes = hour * 60 + minute
        
        # 开盘时间段：9:30-11:30 (570-690分钟), 13:00-15:00 (780-900分钟)
        morning_start = 9 * 60 + 30  # 570
        morning_end = 11 * 60 + 30   # 690
        afternoon_start = 13 * 60    # 780
        afternoon_end = 15 * 60      # 900
        
        return (morning_start <= total_minutes <= morning_end) or \
               (afternoon_start <= total_minutes <= afternoon_end)
    except:
        return False

def is_auction_time(time_str):
    """判断时间是否在集合竞价时间段（9:15-9:30）"""
    if not time_str:
        return False
    
    try:
        time_parts = time_str.split(':')
        if len(time_parts) != 2:
            return False
        
        hour = int(time_parts[0])
        minute = int(time_parts[1])
        total_minutes = hour * 60 + minute
        
        # 集合竞价时间段：9:15-9:30 (555-570分钟)
        auction_start = 9 * 60 + 15  # 555
        auction_end = 9 * 60 + 30    # 570
        
        return auction_start <= total_minutes < auction_end
    except:
        return False

def generate_comprehensive_test_data():
    """生成包含连板和市场数据的综合测试数据"""
    
    # 生成连板测试数据
    lianban_data = []
    market_data = []
    
    # 生成9:15-15:00的测试数据，每5分钟一个数据点
    start_hour, start_minute = 9, 15
    end_hour, end_minute = 15, 0
    
    current_hour, current_minute = start_hour, start_minute
    
    while (current_hour * 60 + current_minute) <= (end_hour * 60 + end_minute):
        time_str = f"{current_hour:02d}:{current_minute:02d}"
        
        # 跳过午休时间 11:30-13:00
        if not (11 * 60 + 30 < current_hour * 60 + current_minute < 13 * 60):
            # 连板数据
            lianban_data.append({
                'time': time_str,
                'data': {
                    'lianban_progress': {
                        '首板': {'total': 50 + (current_hour * 60 + current_minute) % 20, 'success': 30},
                        '1进2': {'total': 25 + (current_hour * 60 + current_minute) % 10, 'success': 15},
                        '2进3': {'total': 12 + (current_hour * 60 + current_minute) % 5, 'success': 8},
                        '3进4': {'total': 6 + (current_hour * 60 + current_minute) % 3, 'success': 4}
                    }
                }
            })
            
            # 市场数据（包含涨跌停和成交金额信息）
            market_data.append({
                'time': time_str,
                'data': {
                    'up_count': 1200 + (current_hour * 60 + current_minute) % 300,
                    'down_count': 800 + (current_hour * 60 + current_minute) % 200,
                    'limit_up_count': 15 + (current_hour * 60 + current_minute) % 20,
                    'limit_down_count': 8 + (current_hour * 60 + current_minute) % 10,
                    'volume': f"{2.5 + (current_hour * 60 + current_minute) % 20 * 0.1:.1f}万亿",
                    'total_amount': 25000 + (current_hour * 60 + current_minute) % 5000  # 亿元
                }
            })
        
        # 增加5分钟
        current_minute += 5
        if current_minute >= 60:
            current_minute = 0
            current_hour += 1
    
    return lianban_data, market_data

def save_test_data_for_all_modules(lianban_data, market_data):
    """保存所有模块的测试数据"""
    try:
        # 确保目录存在
        realtime_data_path = os.path.join(os.path.dirname(__file__), 'show_data', 'realtime_data')
        os.makedirs(realtime_data_path, exist_ok=True)
        
        today = datetime.now().strftime('%Y-%m-%d')
        
        # 保存连板数据
        lianban_filename = f"realtime_lianban_{today}.json"
        lianban_filepath = os.path.join(realtime_data_path, lianban_filename)
        with open(lianban_filepath, 'w', encoding='utf-8') as f:
            json.dump(lianban_data, f, ensure_ascii=False, indent=2)
        
        # 保存市场数据（用于市场概况、涨跌停、成交金额三个模块）
        market_filename = f"realtime_market_{today}.json"
        market_filepath = os.path.join(realtime_data_path, market_filename)
        with open(market_filepath, 'w', encoding='utf-8') as f:
            json.dump(market_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 连板测试数据已保存到: {lianban_filepath}")
        print(f"✅ 市场测试数据已保存到: {market_filepath}")
        return True
    except Exception as e:
        print(f"❌ 保存测试数据失败: {e}")
        return False

def test_module_api(module_name, api_endpoint):
    """测试单个模块的API"""
    try:
        base_url = "http://localhost:8080"
        response = requests.get(f"{base_url}{api_endpoint}", timeout=10)
        result = response.json()
        
        if result.get('success'):
            api_data = result['data']
            
            # 分析数据
            auction_count = 0
            market_count = 0
            other_count = 0
            
            for item in api_data:
                time_str = item['time']
                if is_auction_time(time_str):
                    auction_count += 1
                elif is_market_open_time(time_str):
                    market_count += 1
                else:
                    other_count += 1
            
            # 验证过滤效果
            filter_success = auction_count == 0 and market_count > 0
            
            print(f"📊 {module_name}:")
            print(f"   API返回: {len(api_data)} 个数据点")
            print(f"   集合竞价时间段: {auction_count} 个")
            print(f"   正常开盘时间段: {market_count} 个")
            print(f"   其他时间段: {other_count} 个")
            print(f"   过滤效果: {'✅ 正确' if filter_success else '❌ 异常'}")
            
            # 显示时间范围
            if api_data:
                first_time = api_data[0]['time']
                last_time = api_data[-1]['time']
                print(f"   时间范围: {first_time} - {last_time}")
            
            return filter_success, len(api_data), auction_count, market_count
        else:
            print(f"❌ {module_name} API请求失败: {result.get('error')}")
            return False, 0, 0, 0
    except Exception as e:
        print(f"❌ {module_name} API测试异常: {e}")
        return False, 0, 0, 0

def test_all_trend_modules():
    """测试所有四个趋势模块"""
    print("🧪 测试程序03：验证所有四个趋势模块的时间过滤效果")
    print("=" * 70)
    
    # 生成综合测试数据
    print("📊 生成综合测试数据...")
    lianban_data, market_data = generate_comprehensive_test_data()
    
    print(f"   连板数据: {len(lianban_data)} 个数据点")
    print(f"   市场数据: {len(market_data)} 个数据点")
    
    # 分析原始数据
    lianban_auction = sum(1 for item in lianban_data if is_auction_time(item['time']))
    lianban_market = sum(1 for item in lianban_data if is_market_open_time(item['time']))
    market_auction = sum(1 for item in market_data if is_auction_time(item['time']))
    market_market = sum(1 for item in market_data if is_market_open_time(item['time']))
    
    print(f"   连板数据中集合竞价时间段: {lianban_auction} 个")
    print(f"   连板数据中正常开盘时间段: {lianban_market} 个")
    print(f"   市场数据中集合竞价时间段: {market_auction} 个")
    print(f"   市场数据中正常开盘时间段: {market_market} 个")
    
    # 保存测试数据
    print("\n💾 保存测试数据...")
    if not save_test_data_for_all_modules(lianban_data, market_data):
        return False
    
    # 测试所有四个模块的API
    print("\n🌐 测试所有趋势模块API...")
    
    modules = [
        ("连板趋势图", "/api/realtime/history/lianban"),
        ("市场概况趋势图", "/api/realtime/history/market"),
        ("涨跌停趋势图", "/api/realtime/history/market"),  # 使用市场数据
        ("成交金额趋势图", "/api/realtime/history/market")   # 使用市场数据
    ]
    
    all_success = True
    results = []
    
    for module_name, api_endpoint in modules:
        success, total, auction, market = test_module_api(module_name, api_endpoint)
        results.append({
            'name': module_name,
            'success': success,
            'total': total,
            'auction': auction,
            'market': market
        })
        if not success:
            all_success = False
        print()
    
    # 总结测试结果
    print("🎯 测试总结:")
    print("=" * 50)
    
    for result in results:
        status = "✅ 通过" if result['success'] else "❌ 失败"
        print(f"   {result['name']}: {status}")
        print(f"      数据点: {result['total']}, 集合竞价: {result['auction']}, 开盘时间: {result['market']}")
    
    print(f"\n总体结果: {'✅ 所有模块过滤功能正常' if all_success else '❌ 部分模块过滤功能异常'}")
    
    # 验证关键要求
    print("\n📋 关键要求验证:")
    print("   ✅ 实时数据从9:30开始显示" if all(r['auction'] == 0 for r in results) else "   ❌ 仍包含9:15-9:30集合竞价数据")
    print("   ✅ 集合竞价数据已被过滤" if all(r['auction'] == 0 for r in results) else "   ❌ 集合竞价数据未被完全过滤")
    print("   ✅ 正常开盘时间数据正常显示" if all(r['market'] > 0 for r in results) else "   ❌ 正常开盘时间数据异常")
    print("   ✅ 所有四个趋势模块都应用了过滤" if all_success else "   ❌ 部分趋势模块未应用过滤")
    
    return all_success

if __name__ == "__main__":
    test_all_trend_modules()
