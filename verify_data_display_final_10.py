#!/usr/bin/env python3
"""
验证程序10: 最终验证数据显示修复
确保前端页面能正确显示所有数据
"""

import requests
import json
import time
from datetime import datetime
import webbrowser
import os

def verify_all_apis():
    """验证所有API端点"""
    print("🔍 验证所有API端点...")
    print("=" * 60)
    
    base_url = "http://localhost:8080"
    
    apis = [
        '/api/lianban_progress',
        '/api/market_summary', 
        '/api/limit_stats',
        '/api/realtime/lianban_progress',
        '/api/realtime/market_summary',
        '/api/realtime/history/market',
        '/api/realtime/history/lianban'
    ]
    
    all_ok = True
    
    for api in apis:
        try:
            response = requests.get(f"{base_url}{api}", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('success', True):  # 有些API没有success字段但返回正常数据
                    print(f"   ✅ {api}")
                else:
                    print(f"   ❌ {api} - {data.get('error', '未知错误')}")
                    all_ok = False
            else:
                print(f"   ❌ {api} - HTTP {response.status_code}")
                all_ok = False
        except Exception as e:
            print(f"   ❌ {api} - {e}")
            all_ok = False
    
    return all_ok

def verify_data_files():
    """验证数据文件完整性"""
    print("\n📁 验证数据文件完整性...")
    print("=" * 60)
    
    data_dirs = [
        ('大板块数据', 'show_data/dabanke_data'),
        ('市场数据', 'show_data/market_data'),
        ('实时数据', 'show_data/realtime_data')
    ]
    
    all_ok = True
    
    for name, path in data_dirs:
        if os.path.exists(path):
            files = [f for f in os.listdir(path) if f.endswith('.json')]
            print(f"   ✅ {name}: {len(files)} 个文件")
        else:
            print(f"   ❌ {name}: 目录不存在")
            all_ok = False
    
    return all_ok

def verify_frontend_functionality():
    """验证前端功能"""
    print("\n🌐 验证前端功能...")
    print("=" * 60)
    
    try:
        # 检查主页面
        response = requests.get("http://localhost:8080/", timeout=10)
        if response.status_code == 200:
            content = response.text
            
            # 检查关键JavaScript函数
            js_functions = [
                'loadLianbanData',
                'loadMarketData', 
                'loadLimitData',
                'loadRealtimeHistory',
                'updateMergedCharts'
            ]
            
            missing_functions = []
            for func in js_functions:
                if func not in content:
                    missing_functions.append(func)
            
            if not missing_functions:
                print("   ✅ 所有关键JavaScript函数都存在")
            else:
                print(f"   ⚠️ 缺少JavaScript函数: {missing_functions}")
            
            # 检查图表容器
            chart_containers = [
                'lianbanMergedChart',
                'marketMergedChart',
                'limitMergedChart',
                'volumeMergedChart'
            ]
            
            missing_containers = []
            for container in chart_containers:
                if container not in content:
                    missing_containers.append(container)
            
            if not missing_containers:
                print("   ✅ 所有图表容器都存在")
            else:
                print(f"   ⚠️ 缺少图表容器: {missing_containers}")
                
            return len(missing_functions) == 0 and len(missing_containers) == 0
        else:
            print(f"   ❌ 主页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 前端验证异常: {e}")
        return False

def create_test_summary():
    """创建测试总结"""
    print("\n📋 创建测试总结...")
    print("=" * 60)
    
    summary = {
        "test_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "test_results": {
            "apis_working": verify_all_apis(),
            "data_files_complete": verify_data_files(),
            "frontend_functional": verify_frontend_functionality()
        },
        "fixes_applied": [
            "生成了缺失的大板块数据（dabanke_data目录）",
            "修复了实时市场概况API超时问题",
            "优化了API性能，移除了外部依赖",
            "确保了数据时间过滤正确（过滤9:30之前的数据）",
            "验证了所有API端点正常工作"
        ],
        "api_endpoints": [
            "/api/lianban_progress - 连板进度数据",
            "/api/market_summary - 市场概况数据",
            "/api/limit_stats - 涨跌停统计数据", 
            "/api/realtime/lianban_progress - 实时连板进度数据",
            "/api/realtime/market_summary - 实时市场概况数据",
            "/api/realtime/history/market - 实时市场历史数据",
            "/api/realtime/history/lianban - 实时连板历史数据"
        ]
    }
    
    # 保存总结到文件
    with open('data_display_fix_summary_10.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    print("   ✅ 测试总结已保存到 data_display_fix_summary_10.json")
    
    return summary

def open_browser_for_verification():
    """打开浏览器进行最终验证"""
    print("\n🌐 打开浏览器进行最终验证...")
    print("=" * 60)
    
    url = "http://localhost:8080"
    
    try:
        print(f"   正在打开: {url}")
        webbrowser.open(url)
        print("   ✅ 浏览器已打开")
        print("   📝 请在浏览器中检查:")
        print("      - 连板进度图表是否显示")
        print("      - 市场概况图表是否显示") 
        print("      - 涨跌停统计图表是否显示")
        print("      - 实时数据是否正常更新")
        print("      - 数据时间范围是否正确（9:30之后）")
    except Exception as e:
        print(f"   ⚠️ 无法自动打开浏览器: {e}")
        print(f"   📝 请手动访问: {url}")

def main():
    """主函数"""
    print("🚀 开始最终验证数据显示修复")
    print("=" * 60)
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 等待服务器启动
    print("\n⏳ 等待服务器启动...")
    time.sleep(2)
    
    # 执行验证
    apis_ok = verify_all_apis()
    files_ok = verify_data_files()
    frontend_ok = verify_frontend_functionality()
    
    # 创建测试总结
    summary = create_test_summary()
    
    # 显示最终结果
    print("\n\n✅ 最终验证结果")
    print("=" * 60)
    
    if apis_ok and files_ok and frontend_ok:
        print("🎉 数据显示问题已完全修复！")
        print("\n📊 修复内容:")
        for fix in summary['fixes_applied']:
            print(f"   ✅ {fix}")
        
        print(f"\n🔗 可用的API端点 ({len(summary['api_endpoints'])} 个):")
        for endpoint in summary['api_endpoints']:
            print(f"   📡 {endpoint}")
        
        # 打开浏览器验证
        open_browser_for_verification()
        
    else:
        print("⚠️ 部分问题仍未解决:")
        if not apis_ok:
            print("   ❌ API端点有问题")
        if not files_ok:
            print("   ❌ 数据文件不完整")
        if not frontend_ok:
            print("   ❌ 前端功能有问题")
    
    print("\n📄 详细测试报告已保存到: data_display_fix_summary_10.json")

if __name__ == "__main__":
    main()
