#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试程序05 - 涨跌停趋势图颜色修复最终验证
验证修复后的实际效果
"""

import os
import re
from datetime import datetime

def verify_color_fix():
    """验证颜色修复"""
    print("🎨 测试程序05 - 涨跌停趋势图颜色修复最终验证")
    print("=" * 70)
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 检查关键函数的颜色配置
    js_file = "show_data/static/js/charts.js"
    
    if not os.path.exists(js_file):
        print(f"❌ JavaScript文件不存在: {js_file}")
        return False
    
    try:
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查主要的涨跌停图表函数
        main_function_check = check_main_function(content)
        
        # 检查颜色常量定义
        color_constants_check = check_color_constants(content)
        
        # 显示结果
        print_verification_results(main_function_check, color_constants_check)
        
        # 生成修复报告
        generate_fix_report()
        
        return True
        
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        return False

def check_main_function(content: str) -> dict:
    """检查主要函数的颜色配置"""
    print("🔍 检查主要涨跌停图表函数...")
    
    # 查找 updateLimitMergedChart 函数
    func_pattern = r'function\s+updateLimitMergedChart\s*\([^)]*\)\s*\{(.*?)\}'
    func_match = re.search(func_pattern, content, re.DOTALL)
    
    if not func_match:
        return {
            'function_exists': False,
            'limit_up_color': False,
            'limit_down_color': False,
            'realtime_limit_up_color': False,
            'realtime_limit_down_color': False
        }
    
    func_content = func_match.group(1)
    
    # 检查历史数据的颜色配置
    limit_up_pattern = r'label:\s*[\'"]涨停[\'"].*?backgroundColor:\s*colors\.danger'
    limit_down_pattern = r'label:\s*[\'"]跌停[\'"].*?backgroundColor:\s*colors\.success'
    
    limit_up_found = bool(re.search(limit_up_pattern, func_content, re.DOTALL))
    limit_down_found = bool(re.search(limit_down_pattern, func_content, re.DOTALL))
    
    # 检查实时数据的颜色配置
    realtime_limit_up_pattern = r'label:\s*[\'"]涨停\(实时\)[\'"].*?backgroundColor:\s*colors\.danger'
    realtime_limit_down_pattern = r'label:\s*[\'"]跌停\(实时\)[\'"].*?backgroundColor:\s*colors\.success'
    
    realtime_limit_up_found = bool(re.search(realtime_limit_up_pattern, func_content, re.DOTALL))
    realtime_limit_down_found = bool(re.search(realtime_limit_down_pattern, func_content, re.DOTALL))
    
    print(f"  ✅ 函数存在: updateLimitMergedChart")
    print(f"  {'✅' if limit_up_found else '❌'} 历史涨停颜色: {'正确 (红色)' if limit_up_found else '错误'}")
    print(f"  {'✅' if limit_down_found else '❌'} 历史跌停颜色: {'正确 (绿色)' if limit_down_found else '错误'}")
    print(f"  {'✅' if realtime_limit_up_found else '❌'} 实时涨停颜色: {'正确 (红色)' if realtime_limit_up_found else '错误'}")
    print(f"  {'✅' if realtime_limit_down_found else '❌'} 实时跌停颜色: {'正确 (绿色)' if realtime_limit_down_found else '错误'}")
    
    return {
        'function_exists': True,
        'limit_up_color': limit_up_found,
        'limit_down_color': limit_down_found,
        'realtime_limit_up_color': realtime_limit_up_found,
        'realtime_limit_down_color': realtime_limit_down_found
    }

def check_color_constants(content: str) -> dict:
    """检查颜色常量定义"""
    print("\n🎨 检查颜色常量定义...")
    
    # 查找颜色常量定义
    colors_pattern = r'const\s+colors\s*=\s*\{([^}]+)\}'
    colors_match = re.search(colors_pattern, content, re.DOTALL)
    
    if not colors_match:
        print("  ❌ 未找到颜色常量定义")
        return {'colors_defined': False, 'danger_color': False, 'success_color': False}
    
    colors_content = colors_match.group(1)
    
    # 检查具体颜色值
    danger_pattern = r'danger:\s*[\'"]#dc3545[\'"]'
    success_pattern = r'success:\s*[\'"]#28a745[\'"]'
    
    danger_found = bool(re.search(danger_pattern, colors_content))
    success_found = bool(re.search(success_pattern, colors_content))
    
    print(f"  ✅ 颜色常量定义存在")
    print(f"  {'✅' if danger_found else '❌'} danger颜色: {'#dc3545 (红色)' if danger_found else '未找到或错误'}")
    print(f"  {'✅' if success_found else '❌'} success颜色: {'#28a745 (绿色)' if success_found else '未找到或错误'}")
    
    return {
        'colors_defined': True,
        'danger_color': danger_found,
        'success_color': success_found
    }

def print_verification_results(main_func: dict, colors: dict):
    """打印验证结果"""
    print("\n📊 验证结果汇总:")
    print("-" * 50)
    
    # 计算通过的测试数量
    main_func_passed = sum([
        main_func['function_exists'],
        main_func['limit_up_color'],
        main_func['limit_down_color'],
        main_func['realtime_limit_up_color'],
        main_func['realtime_limit_down_color']
    ])
    
    colors_passed = sum([
        colors['colors_defined'],
        colors['danger_color'],
        colors['success_color']
    ])
    
    total_passed = main_func_passed + colors_passed
    total_tests = 8
    
    print(f"主要函数配置: {main_func_passed}/5 项通过")
    print(f"颜色常量配置: {colors_passed}/3 项通过")
    print(f"总体通过率: {total_passed}/{total_tests} ({total_passed/total_tests*100:.1f}%)")
    
    if total_passed == total_tests:
        print("\n🎉 所有配置都已正确修复！")
    elif total_passed >= 6:
        print("\n✅ 主要配置已修复，部分细节可能需要调整")
    else:
        print("\n⚠️  仍有重要配置需要修复")

def generate_fix_report():
    """生成修复报告"""
    print("\n📋 涨跌停趋势图颜色修复报告")
    print("=" * 60)
    
    print("🎯 修复目标:")
    print("- 涨停显示红色，符合中国股市习惯")
    print("- 跌停显示绿色，符合中国股市习惯")
    print("- 确保历史数据和实时数据颜色一致")
    
    print("\n🔧 已修复的内容:")
    modifications = [
        "updateLimitMergedChart函数中的历史数据颜色配置",
        "updateLimitMergedChart函数中的实时数据颜色配置",
        "涨停：colors.success → colors.danger (绿色 → 红色)",
        "跌停：colors.danger → colors.success (红色 → 绿色)"
    ]
    
    for i, mod in enumerate(modifications, 1):
        print(f"{i}. {mod}")
    
    print("\n📊 颜色标准:")
    print("- 涨停：#dc3545 (红色) - 代表上涨、积极")
    print("- 跌停：#28a745 (绿色) - 代表下跌、冷静")
    
    print("\n🧪 测试建议:")
    print("1. 打开 test_limit_color_05.html 查看修复效果")
    print("2. 检查涨跌停趋势图的颜色显示")
    print("3. 验证数据条的颜色是否一致")
    print("4. 确认符合中国股市的视觉习惯")

def check_files_exist():
    """检查相关文件是否存在"""
    files_to_check = [
        "show_data/static/js/charts.js",
        "show_data/static/css/style.css",
        "test_limit_color_05.html",
        "test_color_fix_05.py"
    ]
    
    print("📂 文件存在性检查:")
    all_exist = True
    
    for file_path in files_to_check:
        exists = os.path.exists(file_path)
        status = "✅" if exists else "❌"
        print(f"{status} {file_path}")
        if not exists:
            all_exist = False
    
    return all_exist

if __name__ == "__main__":
    print("🚀 测试程序05 - 涨跌停趋势图颜色修复最终验证")
    print()
    
    # 检查文件
    if check_files_exist():
        print("✅ 所有必要文件都存在\n")
    else:
        print("❌ 部分文件缺失\n")
    
    # 执行验证
    success = verify_color_fix()
    
    if success:
        print("\n🎉 颜色修复验证完成！")
        print("请在浏览器中查看 test_limit_color_05.html 确认视觉效果。")
    else:
        print("\n❌ 验证过程中出现问题，请检查相关文件。")
