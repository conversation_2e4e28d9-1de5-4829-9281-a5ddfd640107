<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试程序05 - 涨跌停趋势图颜色修复验证</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="show_data/static/css/style.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .color-demo {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 6px;
            background: #f8f9fa;
        }
        .color-box {
            width: 30px;
            height: 30px;
            border-radius: 4px;
            border: 2px solid #ddd;
        }
        .color-box.limit-up {
            background: #dc3545;
        }
        .color-box.limit-down {
            background: #28a745;
        }
        .test-info {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin-bottom: 20px;
        }
        .chart-container {
            height: 300px;
            margin-bottom: 20px;
        }
        .comparison-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .before-after {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
        }
        .before {
            border-color: #dc3545;
        }
        .after {
            border-color: #28a745;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-info">
            <h4>🎨 测试程序05 - 涨跌停趋势图颜色修复验证</h4>
            <p><strong>修复目标：</strong>确保涨跌停趋势图显示正确的颜色</p>
            <ul>
                <li>涨停：红色 (#dc3545)</li>
                <li>跌停：绿色 (#28a745)</li>
            </ul>
        </div>

        <!-- 颜色对比说明 -->
        <div class="test-card">
            <h5>🎯 颜色标准</h5>
            <div class="color-demo">
                <div class="color-box limit-up"></div>
                <div>
                    <strong>涨停</strong>：红色 (#dc3545)<br>
                    <small>表示股票涨停，使用红色符合中国股市习惯</small>
                </div>
            </div>
            <div class="color-demo">
                <div class="color-box limit-down"></div>
                <div>
                    <strong>跌停</strong>：绿色 (#28a745)<br>
                    <small>表示股票跌停，使用绿色符合中国股市习惯</small>
                </div>
            </div>
        </div>

        <!-- 修复前后对比 -->
        <div class="test-card">
            <h5>🔄 修复前后对比</h5>
            <div class="comparison-container">
                <div class="before-after before">
                    <h6>修复前（错误）</h6>
                    <ul>
                        <li>涨停：绿色 ❌</li>
                        <li>跌停：红色 ❌</li>
                        <li>颜色与中国股市习惯相反</li>
                    </ul>
                </div>
                <div class="before-after after">
                    <h6>修复后（正确）</h6>
                    <ul>
                        <li>涨停：红色 ✅</li>
                        <li>跌停：绿色 ✅</li>
                        <li>颜色符合中国股市习惯</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 涨跌停趋势图 -->
        <div class="test-card">
            <h5>📊 涨跌停趋势图（修复后）</h5>
            <div class="chart-container">
                <canvas id="limitChart"></canvas>
            </div>
        </div>

        <!-- 涨跌停数据条 -->
        <div class="test-card">
            <h5>📈 涨跌停数据条</h5>
            <div id="limit-bars" class="mb-3"></div>
            <div class="alert alert-info">
                <strong>说明：</strong>数据条也使用相同的颜色标准，确保整体视觉一致性。
            </div>
        </div>

        <!-- 测试结果 -->
        <div class="test-card">
            <h5>✅ 测试结果</h5>
            <div id="test-results">
                <div class="alert alert-info">
                    <strong>测试进行中...</strong> 正在验证颜色修复效果
                </div>
            </div>
        </div>

        <!-- 颜色验证工具 -->
        <div class="test-card">
            <h5>🔍 颜色验证工具</h5>
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-primary w-100 mb-2" onclick="checkChartColors()">检查图表颜色</button>
                    <button class="btn btn-success w-100 mb-2" onclick="checkBarColors()">检查数据条颜色</button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-warning w-100 mb-2" onclick="simulateData()">模拟测试数据</button>
                    <button class="btn btn-info w-100 mb-2" onclick="loadRealData()">加载真实数据</button>
                </div>
            </div>
            <div id="check-results" class="mt-3"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="show_data/static/js/charts.js"></script>
    <script>
        let testLimitChart;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 测试程序05启动 - 涨跌停趋势图颜色修复验证');
            
            // 初始化测试图表
            initTestChart();
            
            // 模拟测试数据
            simulateData();
            
            // 延迟检查颜色
            setTimeout(checkColorCorrectness, 2000);
        });

        // 初始化测试图表
        function initTestChart() {
            const ctx = document.getElementById('limitChart').getContext('2d');
            testLimitChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: []
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '涨跌停趋势图'
                        },
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '数量'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '日期'
                            }
                        }
                    }
                }
            });
        }

        // 模拟测试数据
        function simulateData() {
            const testData = [];
            const today = new Date();
            
            for (let i = 6; i >= 0; i--) {
                const date = new Date(today);
                date.setDate(date.getDate() - i);
                
                testData.push({
                    date: date.toISOString().split('T')[0],
                    limit_up_total: Math.floor(Math.random() * 50) + 10,
                    limit_down_total: Math.floor(Math.random() * 30) + 5
                });
            }
            
            updateTestChart(testData);
            updateTestBars(testData);
            
            console.log('📊 模拟数据已生成:', testData);
        }

        // 更新测试图表
        function updateTestChart(data) {
            const labels = data.map(item => item.date);
            const limitUpData = data.map(item => item.limit_up_total);
            const limitDownData = data.map(item => item.limit_down_total);
            
            testLimitChart.data.labels = labels;
            testLimitChart.data.datasets = [
                {
                    label: '涨停',
                    data: limitUpData,
                    backgroundColor: '#dc3545',
                    borderColor: '#dc3545',
                    borderWidth: 1
                },
                {
                    label: '跌停',
                    data: limitDownData,
                    backgroundColor: '#28a745',
                    borderColor: '#28a745',
                    borderWidth: 1
                }
            ];
            testLimitChart.update();
        }

        // 更新测试数据条
        function updateTestBars(data) {
            const latest = data[data.length - 1];
            const limitUp = latest.limit_up_total;
            const limitDown = latest.limit_down_total;
            const total = limitUp + limitDown;
            
            const limitUpPercent = (limitUp / total * 100);
            const limitDownPercent = (limitDown / total * 100);
            
            const html = `
                <div class="limit-bar-row">
                    <span class="bar-label rise">涨停 ${limitUp}</span>
                    <span class="bar-label fall">跌停 ${limitDown}</span>
                </div>
                <div class="bar-container">
                    <div class="bar-segment limit-up" style="width: ${limitUpPercent}%"></div>
                    <div class="bar-segment limit-down" style="width: ${limitDownPercent}%"></div>
                </div>
            `;
            
            document.getElementById('limit-bars').innerHTML = html;
        }

        // 检查颜色正确性
        function checkColorCorrectness() {
            const results = [];
            
            // 检查图表颜色
            if (testLimitChart && testLimitChart.data.datasets.length >= 2) {
                const limitUpColor = testLimitChart.data.datasets[0].backgroundColor;
                const limitDownColor = testLimitChart.data.datasets[1].backgroundColor;
                
                results.push({
                    test: '图表涨停颜色',
                    expected: '#dc3545',
                    actual: limitUpColor,
                    passed: limitUpColor === '#dc3545'
                });
                
                results.push({
                    test: '图表跌停颜色',
                    expected: '#28a745',
                    actual: limitDownColor,
                    passed: limitDownColor === '#28a745'
                });
            }
            
            // 检查CSS样式
            const limitUpElement = document.querySelector('.bar-segment.limit-up');
            const limitDownElement = document.querySelector('.bar-segment.limit-down');
            
            if (limitUpElement) {
                const computedStyle = window.getComputedStyle(limitUpElement);
                const bgColor = computedStyle.background;
                results.push({
                    test: 'CSS涨停背景',
                    expected: '包含红色渐变',
                    actual: bgColor.includes('rgb(220, 53, 69)') ? '红色渐变' : bgColor,
                    passed: bgColor.includes('rgb(220, 53, 69)')
                });
            }
            
            if (limitDownElement) {
                const computedStyle = window.getComputedStyle(limitDownElement);
                const bgColor = computedStyle.background;
                results.push({
                    test: 'CSS跌停背景',
                    expected: '包含绿色渐变',
                    actual: bgColor.includes('rgb(40, 167, 69)') ? '绿色渐变' : bgColor,
                    passed: bgColor.includes('rgb(40, 167, 69)')
                });
            }
            
            displayTestResults(results);
        }

        // 显示测试结果
        function displayTestResults(results) {
            const container = document.getElementById('test-results');
            const passedTests = results.filter(r => r.passed).length;
            const totalTests = results.length;
            
            let html = `
                <div class="alert ${passedTests === totalTests ? 'alert-success' : 'alert-warning'}">
                    <strong>颜色验证完成：</strong> ${passedTests}/${totalTests} 项测试通过
                </div>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>测试项目</th>
                                <th>期望值</th>
                                <th>实际值</th>
                                <th>结果</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            results.forEach(result => {
                html += `
                    <tr class="${result.passed ? 'table-success' : 'table-danger'}">
                        <td>${result.test}</td>
                        <td>${result.expected}</td>
                        <td>${result.actual}</td>
                        <td>${result.passed ? '✅ 通过' : '❌ 失败'}</td>
                    </tr>
                `;
            });
            
            html += '</tbody></table></div>';
            container.innerHTML = html;
        }

        // 检查图表颜色
        function checkChartColors() {
            if (!testLimitChart) {
                document.getElementById('check-results').innerHTML = 
                    '<div class="alert alert-warning">图表未初始化</div>';
                return;
            }
            
            const datasets = testLimitChart.data.datasets;
            const results = [];
            
            datasets.forEach((dataset, index) => {
                results.push(`${dataset.label}: ${dataset.backgroundColor}`);
            });
            
            document.getElementById('check-results').innerHTML = 
                '<div class="alert alert-info"><strong>图表颜色检查：</strong><br>' + 
                results.join('<br>') + '</div>';
        }

        // 检查数据条颜色
        function checkBarColors() {
            const limitUpElement = document.querySelector('.bar-segment.limit-up');
            const limitDownElement = document.querySelector('.bar-segment.limit-down');
            
            const results = [];
            
            if (limitUpElement) {
                const style = window.getComputedStyle(limitUpElement);
                results.push(`涨停数据条: ${style.background}`);
            }
            
            if (limitDownElement) {
                const style = window.getComputedStyle(limitDownElement);
                results.push(`跌停数据条: ${style.background}`);
            }
            
            document.getElementById('check-results').innerHTML = 
                '<div class="alert alert-success"><strong>数据条颜色检查：</strong><br>' + 
                results.join('<br>') + '</div>';
        }

        // 加载真实数据
        function loadRealData() {
            fetch('/api/limit_stats')
                .then(response => response.json())
                .then(result => {
                    if (result.success && result.data) {
                        updateTestChart(result.data);
                        updateTestBars(result.data);
                        document.getElementById('check-results').innerHTML = 
                            '<div class="alert alert-success">真实数据加载成功</div>';
                    } else {
                        throw new Error(result.error || '数据加载失败');
                    }
                })
                .catch(error => {
                    document.getElementById('check-results').innerHTML = 
                        '<div class="alert alert-danger">真实数据加载失败: ' + error.message + '</div>';
                });
        }
    </script>
</body>
</html>
