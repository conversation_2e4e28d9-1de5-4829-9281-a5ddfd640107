#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试程序01：验证实时数据时间过滤功能
测试集合竞价时间段(9:15-9:30)数据是否被正确过滤
"""

import os
import sys
import json
import requests
from datetime import datetime, time

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def is_market_open_time(time_str):
    """判断时间是否在开盘时间（9:30-11:30, 13:00-15:00）"""
    if not time_str:
        return False
    
    try:
        # 解析时间字符串 (HH:MM 格式)
        time_parts = time_str.split(':')
        if len(time_parts) != 2:
            return False
        
        hour = int(time_parts[0])
        minute = int(time_parts[1])
        total_minutes = hour * 60 + minute
        
        # 开盘时间段：9:30-11:30 (570-690分钟), 13:00-15:00 (780-900分钟)
        morning_start = 9 * 60 + 30  # 570
        morning_end = 11 * 60 + 30   # 690
        afternoon_start = 13 * 60    # 780
        afternoon_end = 15 * 60      # 900
        
        return (morning_start <= total_minutes <= morning_end) or \
               (afternoon_start <= total_minutes <= afternoon_end)
    except:
        return False

def is_auction_time(time_str):
    """判断时间是否在集合竞价时间段（9:15-9:30）"""
    if not time_str:
        return False
    
    try:
        time_parts = time_str.split(':')
        if len(time_parts) != 2:
            return False
        
        hour = int(time_parts[0])
        minute = int(time_parts[1])
        total_minutes = hour * 60 + minute
        
        # 集合竞价时间段：9:15-9:30 (555-570分钟)
        auction_start = 9 * 60 + 15  # 555
        auction_end = 9 * 60 + 30    # 570
        
        return auction_start <= total_minutes < auction_end  # 注意这里是 < 不是 <=
    except:
        return False

def generate_test_data():
    """生成包含集合竞价时间段的测试数据"""
    test_data = []
    
    # 生成9:15-15:00的测试数据，每5分钟一个数据点
    start_hour, start_minute = 9, 15
    end_hour, end_minute = 15, 0
    
    current_hour, current_minute = start_hour, start_minute
    
    while (current_hour * 60 + current_minute) <= (end_hour * 60 + end_minute):
        time_str = f"{current_hour:02d}:{current_minute:02d}"
        
        # 跳过午休时间 11:30-13:00
        if not (11 * 60 + 30 < current_hour * 60 + current_minute < 13 * 60):
            test_data.append({
                'time': time_str,
                'data': {
                    'up_count': 100 + (current_hour * 60 + current_minute) % 50,
                    'down_count': 80 + (current_hour * 60 + current_minute) % 30,
                    'limit_up_count': 5 + (current_hour * 60 + current_minute) % 10,
                    'limit_down_count': 3 + (current_hour * 60 + current_minute) % 5,
                    'volume': f"{1.2 + (current_hour * 60 + current_minute) % 10 * 0.1:.1f}万亿"
                }
            })
        
        # 增加5分钟
        current_minute += 5
        if current_minute >= 60:
            current_minute = 0
            current_hour += 1
    
    return test_data

def save_test_data(data):
    """保存测试数据到实时数据文件"""
    try:
        # 确保目录存在
        realtime_data_path = os.path.join(os.path.dirname(__file__), 'show_data', 'realtime_data')
        os.makedirs(realtime_data_path, exist_ok=True)
        
        # 保存市场数据
        today = datetime.now().strftime('%Y-%m-%d')
        market_filename = f"realtime_market_{today}.json"
        market_filepath = os.path.join(realtime_data_path, market_filename)
        
        with open(market_filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 测试数据已保存到: {market_filepath}")
        return True
    except Exception as e:
        print(f"❌ 保存测试数据失败: {e}")
        return False

def test_time_filtering():
    """测试时间过滤功能"""
    print("🧪 测试程序01：验证实时数据时间过滤功能")
    print("=" * 60)
    
    # 生成测试数据
    print("📊 生成测试数据...")
    test_data = generate_test_data()
    print(f"   生成了 {len(test_data)} 个数据点")
    
    # 分析数据时间分布
    auction_count = 0
    market_count = 0
    other_count = 0
    
    for item in test_data:
        time_str = item['time']
        if is_auction_time(time_str):
            auction_count += 1
        elif is_market_open_time(time_str):
            market_count += 1
        else:
            other_count += 1
    
    print(f"   集合竞价时间段(9:15-9:30): {auction_count} 个数据点")
    print(f"   正常开盘时间段(9:30-11:30, 13:00-15:00): {market_count} 个数据点")
    print(f"   其他时间段: {other_count} 个数据点")
    
    # 保存测试数据
    print("\n💾 保存测试数据...")
    if save_test_data(test_data):
        print("   测试数据保存成功")
    else:
        print("   测试数据保存失败")
        return False
    
    # 测试API响应
    print("\n🌐 测试API响应...")
    try:
        base_url = "http://localhost:8080"
        response = requests.get(f"{base_url}/api/realtime/history/market", timeout=10)
        result = response.json()
        
        if result.get('success'):
            api_data = result['data']
            print(f"   API返回 {len(api_data)} 个数据点")
            
            # 分析API返回的数据
            api_auction_count = 0
            api_market_count = 0
            api_other_count = 0
            
            for item in api_data:
                time_str = item['time']
                if is_auction_time(time_str):
                    api_auction_count += 1
                elif is_market_open_time(time_str):
                    api_market_count += 1
                else:
                    api_other_count += 1
            
            print(f"   API数据中集合竞价时间段: {api_auction_count} 个数据点")
            print(f"   API数据中正常开盘时间段: {api_market_count} 个数据点")
            print(f"   API数据中其他时间段: {api_other_count} 个数据点")
            
            # 验证过滤效果
            print("\n✅ 过滤效果验证:")
            if api_auction_count == 0:
                print("   ✅ 集合竞价时间段数据已被正确过滤")
            else:
                print(f"   ❌ 集合竞价时间段数据未被过滤，仍有 {api_auction_count} 个数据点")
            
            if api_market_count > 0:
                print("   ✅ 正常开盘时间段数据正常显示")
            else:
                print("   ❌ 正常开盘时间段数据被错误过滤")
            
            # 显示一些样本数据
            if api_data:
                print("\n📋 API返回数据样本:")
                for i, item in enumerate(api_data[:5]):
                    time_str = item['time']
                    is_auction = is_auction_time(time_str)
                    is_market = is_market_open_time(time_str)
                    status = "集合竞价" if is_auction else ("开盘时间" if is_market else "其他")
                    print(f"   {i+1}. {time_str} - {status}")
                
                if len(api_data) > 5:
                    print(f"   ... 还有 {len(api_data) - 5} 个数据点")
        else:
            print(f"   ❌ API请求失败: {result.get('error')}")
            return False
    except Exception as e:
        print(f"   ❌ API测试异常: {e}")
        return False
    
    print("\n🎯 测试总结:")
    print(f"   原始数据: {len(test_data)} 个数据点")
    print(f"   API返回: {len(api_data)} 个数据点")
    print(f"   过滤效果: {'✅ 正确' if api_auction_count == 0 and api_market_count > 0 else '❌ 异常'}")
    
    return True

if __name__ == "__main__":
    test_time_filtering()
