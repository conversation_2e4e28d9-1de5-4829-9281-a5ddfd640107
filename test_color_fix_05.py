#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试程序05 - 涨跌停趋势图颜色修复验证
验证JavaScript代码中的颜色配置是否正确
"""

import re
import os
from typing import Dict, List, Tuple

def test_color_fix():
    """测试颜色修复"""
    print("🎨 测试程序05 - 涨跌停趋势图颜色修复验证")
    print("=" * 60)
    
    js_file = "show_data/static/js/charts.js"
    css_file = "show_data/static/css/style.css"
    
    if not os.path.exists(js_file):
        print(f"❌ JavaScript文件不存在: {js_file}")
        return False
    
    if not os.path.exists(css_file):
        print(f"❌ CSS文件不存在: {css_file}")
        return False
    
    try:
        # 检查JavaScript颜色配置
        js_results = check_javascript_colors(js_file)
        
        # 检查CSS颜色配置
        css_results = check_css_colors(css_file)
        
        # 显示结果
        print_js_results(js_results)
        print_css_results(css_results)
        
        # 总体评估
        js_passed = all(r['passed'] for r in js_results)
        css_passed = all(r['passed'] for r in css_results)
        
        print(f"\n📊 总体结果:")
        print(f"✅ JavaScript颜色配置: {'通过' if js_passed else '失败'}")
        print(f"✅ CSS颜色配置: {'通过' if css_passed else '失败'}")
        
        if js_passed and css_passed:
            print("🎉 所有颜色配置都已正确修复！")
            return True
        else:
            print("⚠️  部分颜色配置可能仍有问题，请检查。")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

def check_javascript_colors(js_file: str) -> List[Dict]:
    """检查JavaScript中的颜色配置"""
    with open(js_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    results = []
    
    # 检查颜色常量定义
    color_constants = {
        'colors.danger': '#dc3545',
        'colors.success': '#28a745'
    }
    
    for color_name, expected_value in color_constants.items():
        pattern = rf'{re.escape(color_name)}.*?[\'"]({expected_value})[\'"]'
        found = bool(re.search(pattern, content, re.IGNORECASE))
        results.append({
            'test': f'颜色常量 {color_name}',
            'expected': expected_value,
            'found': found,
            'passed': found
        })
    
    # 检查涨跌停图表配置
    chart_configs = [
        {
            'function': 'updateLimitChart',
            '涨停_label': '涨停数量',
            '涨停_color': 'colors.danger',
            '跌停_label': '跌停数量',
            '跌停_color': 'colors.success'
        },
        {
            'function': 'updateLimitMergedChart',
            '涨停_label': '涨停',
            '涨停_color': 'colors.danger',
            '跌停_label': '跌停',
            '跌停_color': 'colors.success'
        },
        {
            'function': 'updateRealtimeLimitChart',
            '涨停_label': '涨停',
            '涨停_color': 'colors.danger',
            '跌停_label': '跌停',
            '跌停_color': 'colors.success'
        }
    ]
    
    for config in chart_configs:
        func_name = config['function']
        
        # 查找函数
        func_pattern = rf'function\s+{func_name}\s*\([^)]*\)\s*\{{([^}}]*(?:\{{[^}}]*\}}[^}}]*)*)\}}'
        func_match = re.search(func_pattern, content, re.DOTALL)
        
        if func_match:
            func_content = func_match.group(1)
            
            # 检查涨停配置
            limit_up_pattern = rf'label:\s*[\'"]涨停[^\'\"]*[\'"].*?backgroundColor:\s*colors\.danger'
            limit_up_found = bool(re.search(limit_up_pattern, func_content, re.DOTALL))
            
            results.append({
                'test': f'{func_name} - 涨停颜色',
                'expected': 'colors.danger (红色)',
                'found': limit_up_found,
                'passed': limit_up_found
            })
            
            # 检查跌停配置
            limit_down_pattern = rf'label:\s*[\'"]跌停[^\'\"]*[\'"].*?backgroundColor:\s*colors\.success'
            limit_down_found = bool(re.search(limit_down_pattern, func_content, re.DOTALL))
            
            results.append({
                'test': f'{func_name} - 跌停颜色',
                'expected': 'colors.success (绿色)',
                'found': limit_down_found,
                'passed': limit_down_found
            })
        else:
            results.append({
                'test': f'{func_name} - 函数存在',
                'expected': '函数应该存在',
                'found': False,
                'passed': False
            })
    
    return results

def check_css_colors(css_file: str) -> List[Dict]:
    """检查CSS中的颜色配置"""
    with open(css_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    results = []
    
    # 检查涨跌停相关的CSS类
    css_checks = [
        {
            'selector': '.bar-segment.limit-up',
            'property': 'background',
            'expected_colors': ['#dc3545', '#ef4444'],
            'description': '涨停数据条背景'
        },
        {
            'selector': '.bar-segment.limit-down',
            'property': 'background',
            'expected_colors': ['#28a745', '#22c55e'],
            'description': '跌停数据条背景'
        },
        {
            'selector': '.bar-segment-horizontal.limit-up',
            'property': 'background',
            'expected_colors': ['#dc2626', '#b91c1c'],
            'description': '水平涨停数据条背景'
        },
        {
            'selector': '.bar-segment-horizontal.limit-down',
            'property': 'background',
            'expected_colors': ['#16a34a', '#15803d'],
            'description': '水平跌停数据条背景'
        },
        {
            'selector': '.bar-label.rise',
            'property': 'color',
            'expected_colors': ['#dc3545'],
            'description': '涨停标签文字颜色'
        }
    ]
    
    for check in css_checks:
        selector = check['selector']
        expected_colors = check['expected_colors']
        description = check['description']
        
        # 查找CSS规则
        pattern = rf'{re.escape(selector)}\s*\{{[^}}]*\}}'
        match = re.search(pattern, content, re.DOTALL)
        
        if match:
            rule_content = match.group(0)
            found_colors = []
            
            for color in expected_colors:
                if color.lower() in rule_content.lower():
                    found_colors.append(color)
            
            passed = len(found_colors) > 0
            results.append({
                'test': description,
                'expected': f'包含 {expected_colors}',
                'found': found_colors if found_colors else '未找到预期颜色',
                'passed': passed
            })
        else:
            results.append({
                'test': description,
                'expected': f'CSS规则 {selector}',
                'found': '规则不存在',
                'passed': False
            })
    
    return results

def print_js_results(results: List[Dict]):
    """打印JavaScript测试结果"""
    print("\n🔧 JavaScript颜色配置验证:")
    print("-" * 80)
    
    for i, result in enumerate(results, 1):
        status = "✅ 通过" if result['passed'] else "❌ 失败"
        print(f"{i:2d}. {result['test']}")
        print(f"    期望: {result['expected']}")
        print(f"    结果: {result['found']}")
        print(f"    状态: {status}")
        print()

def print_css_results(results: List[Dict]):
    """打印CSS测试结果"""
    print("\n🎨 CSS颜色配置验证:")
    print("-" * 80)
    
    for i, result in enumerate(results, 1):
        status = "✅ 通过" if result['passed'] else "❌ 失败"
        print(f"{i:2d}. {result['test']}")
        print(f"    期望: {result['expected']}")
        print(f"    结果: {result['found']}")
        print(f"    状态: {status}")
        print()

def analyze_color_standards():
    """分析颜色标准"""
    print("\n📋 中国股市颜色标准:")
    print("-" * 50)
    
    standards = [
        {
            'type': '涨停/上涨',
            'color': '红色 (#dc3545)',
            'reason': '中国股市传统，红色代表上涨、喜庆'
        },
        {
            'type': '跌停/下跌',
            'color': '绿色 (#28a745)',
            'reason': '中国股市传统，绿色代表下跌、冷静'
        },
        {
            'type': '平盘',
            'color': '灰色 (#6c757d)',
            'reason': '中性颜色，表示无明显变化'
        }
    ]
    
    for i, standard in enumerate(standards, 1):
        print(f"{i}. {standard['type']}")
        print(f"   颜色: {standard['color']}")
        print(f"   原因: {standard['reason']}")
        print()

if __name__ == "__main__":
    success = test_color_fix()
    analyze_color_standards()
    
    if success:
        print("\n🎯 建议下一步:")
        print("1. 在浏览器中打开 test_limit_color_05.html 验证视觉效果")
        print("2. 检查涨跌停趋势图的颜色显示")
        print("3. 验证数据条的颜色是否正确")
        print("4. 确认整体视觉效果符合中国股市习惯")
    else:
        print("\n⚠️  需要进一步检查颜色配置")
