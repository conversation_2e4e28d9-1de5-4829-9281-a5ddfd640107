#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试程序04 - CSS样式修改验证
验证连板金字塔布局修复的CSS更改是否正确应用
"""

import re
import os
from typing import Dict, List, Tuple

def test_css_changes():
    """测试CSS样式修改"""
    print("🔧 测试程序04 - CSS样式修改验证")
    print("=" * 50)
    
    css_file = "show_data/static/css/style.css"
    
    if not os.path.exists(css_file):
        print(f"❌ CSS文件不存在: {css_file}")
        return False
    
    try:
        with open(css_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 定义要检查的CSS修改
        expected_changes = [
            {
                'selector': '.lianban-text-display',
                'property': 'padding',
                'expected': '5px',
                'description': '连板文本显示容器内边距'
            },
            {
                'selector': '.overall-success-rate',
                'property': 'padding',
                'expected': '2px 6px',
                'description': '整体胜率内边距'
            },
            {
                'selector': '.overall-success-rate',
                'property': 'margin-bottom',
                'expected': '2px',
                'description': '整体胜率下边距'
            },
            {
                'selector': '.lianban-level-row',
                'property': 'margin-bottom',
                'expected': '1px',
                'description': '级别行下边距'
            },
            {
                'selector': '.lianban-level-row',
                'property': 'padding',
                'expected': '3px 5px',
                'description': '级别行内边距'
            },
            {
                'selector': '.level-stats',
                'property': 'align-items',
                'expected': 'center',
                'description': '级别统计对齐方式'
            }
        ]
        
        results = []
        
        for change in expected_changes:
            found = check_css_property(content, change['selector'], change['property'], change['expected'])
            results.append({
                'description': change['description'],
                'selector': change['selector'],
                'property': change['property'],
                'expected': change['expected'],
                'found': found
            })
        
        # 显示结果
        print_test_results(results)
        
        # 检查移动端样式
        mobile_results = check_mobile_styles(content)
        print_mobile_results(mobile_results)
        
        # 总体评估
        passed_tests = sum(1 for r in results if r['found'])
        total_tests = len(results)
        
        print(f"\n📊 总体结果:")
        print(f"✅ 通过测试: {passed_tests}/{total_tests}")
        print(f"📈 成功率: {passed_tests/total_tests*100:.1f}%")
        
        if passed_tests == total_tests:
            print("🎉 所有CSS修改都已正确应用！")
            return True
        else:
            print("⚠️  部分CSS修改可能未正确应用，请检查。")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

def check_css_property(content: str, selector: str, property: str, expected: str) -> bool:
    """检查CSS属性是否设置正确"""
    # 构建正则表达式来查找CSS规则
    pattern = rf'{re.escape(selector)}\s*\{{[^}}]*?{re.escape(property)}\s*:\s*([^;]+);'
    
    matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
    
    for match in matches:
        # 清理匹配的值
        actual_value = match.strip()
        # 移除注释
        actual_value = re.sub(r'/\*.*?\*/', '', actual_value).strip()
        
        if expected.lower() in actual_value.lower():
            return True
    
    return False

def check_mobile_styles(content: str) -> List[Dict]:
    """检查移动端样式"""
    mobile_checks = [
        {
            'selector': '.pyramid-container-new',
            'property': 'padding',
            'expected': '5px',
            'description': '移动端金字塔容器内边距'
        }
    ]
    
    results = []
    for check in mobile_checks:
        # 在@media查询中查找
        media_pattern = r'@media[^{]*\{[^{}]*(?:\{[^{}]*\}[^{}]*)*' + re.escape(check['selector']) + r'[^{}]*\{[^{}]*' + re.escape(check['property']) + r'\s*:\s*([^;]+);'
        
        matches = re.findall(media_pattern, content, re.DOTALL | re.IGNORECASE)
        found = any(check['expected'].lower() in match.lower() for match in matches)
        
        results.append({
            'description': check['description'],
            'selector': check['selector'],
            'property': check['property'],
            'expected': check['expected'],
            'found': found
        })
    
    return results

def print_test_results(results: List[Dict]):
    """打印测试结果"""
    print("\n🔍 CSS修改验证结果:")
    print("-" * 80)
    
    for i, result in enumerate(results, 1):
        status = "✅ 通过" if result['found'] else "❌ 失败"
        print(f"{i:2d}. {result['description']}")
        print(f"    选择器: {result['selector']}")
        print(f"    属性: {result['property']} = {result['expected']}")
        print(f"    状态: {status}")
        print()

def print_mobile_results(results: List[Dict]):
    """打印移动端测试结果"""
    print("\n📱 移动端样式验证结果:")
    print("-" * 80)
    
    for i, result in enumerate(results, 1):
        status = "✅ 通过" if result['found'] else "❌ 失败"
        print(f"{i:2d}. {result['description']}")
        print(f"    选择器: {result['selector']}")
        print(f"    属性: {result['property']} = {result['expected']}")
        print(f"    状态: {status}")
        print()

def analyze_layout_improvements():
    """分析布局改进效果"""
    print("\n📈 布局改进分析:")
    print("-" * 50)
    
    improvements = [
        {
            'aspect': '空白空间减少',
            'before': '容器padding: 10px, 级别行margin: 2px',
            'after': '容器padding: 5px, 级别行margin: 1px',
            'improvement': '50%空间节省'
        },
        {
            'aspect': '标签对齐统一',
            'before': 'level-stats使用flex-end对齐',
            'after': 'level-stats使用center对齐',
            'improvement': '视觉对齐一致性'
        },
        {
            'aspect': '整体胜率优化',
            'before': '仅text-align: center',
            'after': 'flex布局 + align-items: center',
            'improvement': '更好的垂直对齐'
        },
        {
            'aspect': '移动端适配',
            'before': '移动端padding: 10px',
            'after': '移动端padding: 5px',
            'improvement': '移动端空间优化'
        }
    ]
    
    for i, imp in enumerate(improvements, 1):
        print(f"{i}. {imp['aspect']}")
        print(f"   修改前: {imp['before']}")
        print(f"   修改后: {imp['after']}")
        print(f"   改进效果: {imp['improvement']}")
        print()

if __name__ == "__main__":
    success = test_css_changes()
    analyze_layout_improvements()
    
    if success:
        print("\n🎯 建议下一步:")
        print("1. 在浏览器中打开测试页面验证视觉效果")
        print("2. 检查不同屏幕尺寸下的显示效果")
        print("3. 验证首版股票信息是否完全可见")
        print("4. 确认左右标签是否水平对齐")
    else:
        print("\n⚠️  需要进一步检查CSS修改")
