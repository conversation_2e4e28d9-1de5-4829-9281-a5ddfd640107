#!/usr/bin/env python3
"""
测试程序03：连板金字塔最终验证
验证修复后的连板金字塔功能是否正常工作
"""

import requests
import time
import json

def test_api_responses():
    """测试API响应"""
    print("🔍 测试API响应...")
    
    base_url = "http://localhost:8080"
    endpoints = {
        "/api/lianban_progress": "连板进度数据",
        "/api/market_summary": "市场概况数据", 
        "/api/limit_stats": "涨跌停统计",
        "/api/realtime/lianban_progress": "实时连板数据",
        "/api/realtime/market_summary": "实时市场数据"
    }
    
    results = {}
    
    for endpoint, description in endpoints.items():
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            success = response.status_code == 200
            results[endpoint] = success
            
            status_icon = "✅" if success else "❌"
            print(f"{status_icon} {description}: {response.status_code}")
            
            if success and endpoint == "/api/lianban_progress":
                data = response.json()
                if data.get('success'):
                    print(f"   📊 历史数据: {len(data.get('data', []))} 条记录")
                    if data.get('latest_stocks'):
                        levels = list(data['latest_stocks'].keys())
                        print(f"   📈 股票级别: {levels[:5]}{'...' if len(levels) > 5 else ''}")
                        
                        # 显示一些具体数据
                        for level in levels[:3]:
                            stocks = data['latest_stocks'][level]
                            if stocks:
                                success_count = sum(1 for s in stocks if s.get('status') == '成')
                                print(f"      - {level}: {len(stocks)} 只股票, {success_count} 只成功")
                
        except Exception as e:
            results[endpoint] = False
            print(f"❌ {description}: 请求失败 - {e}")
    
    return results

def test_page_content():
    """测试页面内容"""
    print("\n🔍 测试页面内容...")
    
    try:
        response = requests.get("http://localhost:8080", timeout=10)
        if response.status_code != 200:
            print(f"❌ 页面加载失败: {response.status_code}")
            return False
        
        content = response.text
        
        # 检查关键元素
        checks = {
            "连板金字塔容器": "lianban-pyramid-visual" in content,
            "图表脚本": "charts.js" in content,
            "Bootstrap样式": "bootstrap" in content,
            "Chart.js库": "chart.js" in content or "Chart.js" in content,
            "连板数据API调用": "loadLianbanData" in content or "/api/lianban_progress" in content
        }
        
        for check_name, result in checks.items():
            status_icon = "✅" if result else "❌"
            print(f"{status_icon} {check_name}: {'包含' if result else '缺失'}")
        
        return all(checks.values())
        
    except Exception as e:
        print(f"❌ 页面内容测试失败: {e}")
        return False

def check_javascript_errors():
    """检查JavaScript是否有明显的语法错误"""
    print("\n🔍 检查JavaScript语法...")
    
    js_file = "show_data/static/js/charts.js"
    
    try:
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查基本语法问题
        issues = []
        
        # 检查括号匹配
        open_braces = content.count('{')
        close_braces = content.count('}')
        if open_braces != close_braces:
            issues.append(f"大括号不匹配: {open_braces} 个 '{{' vs {close_braces} 个 '}}'")
        
        open_parens = content.count('(')
        close_parens = content.count(')')
        if open_parens != close_parens:
            issues.append(f"圆括号不匹配: {open_parens} 个 '(' vs {close_parens} 个 ')'")
        
        # 检查关键函数是否存在
        required_functions = [
            'updateModernPyramid',
            'loadLianbanData',
            'updateSuccessRatesDisplay'
        ]
        
        for func in required_functions:
            if f"function {func}" not in content:
                issues.append(f"缺少函数: {func}")
        
        if issues:
            print("❌ 发现语法问题:")
            for issue in issues:
                print(f"   - {issue}")
            return False
        else:
            print("✅ JavaScript语法检查通过")
            return True
            
    except Exception as e:
        print(f"❌ JavaScript检查失败: {e}")
        return False

def test_data_flow():
    """测试数据流是否正常"""
    print("\n🔍 测试数据流...")
    
    try:
        # 测试连板进度API
        response = requests.get("http://localhost:8080/api/lianban_progress", timeout=10)
        if response.status_code != 200:
            print("❌ 连板进度API异常")
            return False
        
        data = response.json()
        if not data.get('success'):
            print("❌ API返回失败状态")
            return False
        
        # 检查数据结构
        if not data.get('data'):
            print("❌ 缺少历史数据")
            return False
        
        if not data.get('latest_stocks'):
            print("❌ 缺少最新股票数据")
            return False
        
        # 检查数据完整性
        latest_data = data['data'][-1] if data['data'] else {}
        if not latest_data.get('progress'):
            print("❌ 最新数据缺少进度信息")
            return False
        
        print("✅ 数据流检查通过")
        print(f"   📊 历史记录: {len(data['data'])} 条")
        print(f"   📈 股票级别: {len(data['latest_stocks'])} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据流测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 连板金字塔最终验证测试")
    print("=" * 60)
    
    # 测试API响应
    api_results = test_api_responses()
    api_ok = all(api_results.values())
    
    # 测试页面内容
    page_ok = test_page_content()
    
    # 检查JavaScript语法
    js_ok = check_javascript_errors()
    
    # 测试数据流
    data_ok = test_data_flow()
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📋 最终测试结果")
    print("=" * 60)
    
    if api_ok:
        print("✅ API端点全部正常")
    else:
        print("❌ 部分API端点异常")
        for endpoint, status in api_results.items():
            if not status:
                print(f"   ❌ {endpoint}")
    
    if page_ok:
        print("✅ 页面内容完整")
    else:
        print("❌ 页面内容缺失")
    
    if js_ok:
        print("✅ JavaScript语法正确")
    else:
        print("❌ JavaScript存在语法问题")
    
    if data_ok:
        print("✅ 数据流正常")
    else:
        print("❌ 数据流异常")
    
    # 最终评估
    all_ok = api_ok and page_ok and js_ok and data_ok
    
    print("\n" + "=" * 60)
    if all_ok:
        print("🎉 修复成功！连板金字塔功能完全正常")
        print("💡 建议：")
        print("   1. 在浏览器中访问 http://localhost:8080 查看效果")
        print("   2. 检查连板金字塔是否正常显示股票数据")
        print("   3. 验证成功率统计是否正确")
        print("   4. 确认不再出现'数据加载失败'错误")
    else:
        print("⚠️ 部分功能仍需修复")
        print("💡 建议检查失败的测试项目")
    
    return all_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
