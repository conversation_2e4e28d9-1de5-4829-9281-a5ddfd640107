#!/usr/bin/env python3
"""
测试程序03：连板金字塔JavaScript错误修复验证
测试连板金字塔模块是否能正常加载和显示数据，不再出现"Assignment to constant variable"错误
"""

import requests
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, WebDriverException

def test_api_endpoints():
    """测试API端点是否正常响应"""
    print("🔍 测试API端点...")
    
    base_url = "http://localhost:5000"
    endpoints = [
        "/api/lianban_progress",
        "/api/market_summary", 
        "/api/limit_stats"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            print(f"✅ {endpoint}: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                if endpoint == "/api/lianban_progress":
                    print(f"   📊 连板数据: {len(data.get('data', []))} 条记录")
                    if data.get('latest_stocks'):
                        print(f"   📈 最新股票数据: {len(data['latest_stocks'])} 个级别")
        except Exception as e:
            print(f"❌ {endpoint}: {e}")
    
    print()

def test_javascript_errors():
    """测试JavaScript错误"""
    print("🔍 测试JavaScript错误...")
    
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    
    driver = None
    try:
        driver = webdriver.Chrome(options=chrome_options)
        driver.get("http://localhost:5000")
        
        # 等待页面加载
        WebDriverWait(driver, 10).wait(
            EC.presence_of_element_located((By.ID, "lianban-pyramid-visual"))
        )
        
        # 等待一段时间让JavaScript执行
        time.sleep(5)
        
        # 获取JavaScript控制台日志
        logs = driver.get_log('browser')
        
        # 分析错误
        js_errors = []
        assignment_errors = []
        
        for log in logs:
            if log['level'] == 'SEVERE':
                js_errors.append(log)
                if 'Assignment to constant variable' in log['message']:
                    assignment_errors.append(log)
        
        print(f"📊 JavaScript日志总数: {len(logs)}")
        print(f"❌ 严重错误数量: {len(js_errors)}")
        print(f"🚫 常量赋值错误: {len(assignment_errors)}")
        
        if assignment_errors:
            print("\n🔍 常量赋值错误详情:")
            for error in assignment_errors:
                print(f"   ❌ {error['message']}")
        
        if js_errors:
            print("\n🔍 其他JavaScript错误:")
            for error in js_errors:
                if 'Assignment to constant variable' not in error['message']:
                    print(f"   ⚠️ {error['message']}")
        
        return len(assignment_errors) == 0, len(js_errors)
        
    except Exception as e:
        print(f"❌ 浏览器测试失败: {e}")
        return False, -1
    finally:
        if driver:
            driver.quit()

def test_pyramid_content():
    """测试金字塔内容是否正确显示"""
    print("🔍 测试金字塔内容显示...")
    
    chrome_options = Options()
    chrome_options.add_argument('--headless')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = None
    try:
        driver = webdriver.Chrome(options=chrome_options)
        driver.get("http://localhost:5000")
        
        # 等待金字塔容器加载
        pyramid_container = WebDriverWait(driver, 15).wait(
            EC.presence_of_element_located((By.ID, "lianban-pyramid-visual"))
        )
        
        # 等待内容加载
        time.sleep(8)
        
        # 检查容器内容
        pyramid_html = pyramid_container.get_attribute('innerHTML')
        
        # 分析内容
        has_error_message = '数据加载失败' in pyramid_html or '加载中' in pyramid_html
        has_pyramid_content = 'pyramid-level' in pyramid_html
        has_success_rates = '%' in pyramid_html
        
        print(f"📊 金字塔HTML长度: {len(pyramid_html)}")
        print(f"❌ 包含错误信息: {has_error_message}")
        print(f"✅ 包含金字塔内容: {has_pyramid_content}")
        print(f"📈 包含成功率数据: {has_success_rates}")
        
        if has_error_message:
            print(f"⚠️ 错误内容: {pyramid_html[:200]}...")
        elif has_pyramid_content:
            # 统计级别数量
            level_count = pyramid_html.count('pyramid-level')
            print(f"📊 显示级别数量: {level_count}")
        
        return not has_error_message and has_pyramid_content
        
    except TimeoutException:
        print("❌ 页面加载超时")
        return False
    except Exception as e:
        print(f"❌ 内容测试失败: {e}")
        return False
    finally:
        if driver:
            driver.quit()

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 连板金字塔JavaScript错误修复验证测试")
    print("=" * 60)
    
    # 测试API端点
    test_api_endpoints()
    
    # 测试JavaScript错误
    print("🔧 测试JavaScript错误修复...")
    no_assignment_errors, total_errors = test_javascript_errors()
    print()
    
    # 测试内容显示
    print("📊 测试内容显示...")
    content_ok = test_pyramid_content()
    print()
    
    # 总结结果
    print("=" * 60)
    print("📋 测试结果总结")
    print("=" * 60)
    
    if no_assignment_errors:
        print("✅ 常量赋值错误已修复")
    else:
        print("❌ 仍存在常量赋值错误")
    
    if total_errors == 0:
        print("✅ 无JavaScript错误")
    elif total_errors > 0:
        print(f"⚠️ 存在 {total_errors} 个JavaScript错误")
    else:
        print("❓ 无法检测JavaScript错误")
    
    if content_ok:
        print("✅ 金字塔内容正常显示")
    else:
        print("❌ 金字塔内容显示异常")
    
    # 最终评估
    if no_assignment_errors and content_ok:
        print("\n🎉 修复成功！连板金字塔功能正常")
        return True
    else:
        print("\n⚠️ 仍需进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
