#!/usr/bin/env python3
"""
测试程序03：连板金字塔API和JavaScript语法验证
简化版测试，验证API响应和JavaScript语法
"""

import requests
import time
import json
import re
import os

def test_api_endpoints():
    """测试API端点是否正常响应"""
    print("🔍 测试API端点...")
    
    base_url = "http://localhost:8080"
    endpoints = [
        "/api/lianban_progress",
        "/api/market_summary", 
        "/api/limit_stats"
    ]
    
    api_results = {}
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            print(f"✅ {endpoint}: {response.status_code}")
            api_results[endpoint] = response.status_code == 200
            
            if response.status_code == 200:
                data = response.json()
                if endpoint == "/api/lianban_progress":
                    print(f"   📊 连板数据: {len(data.get('data', []))} 条记录")
                    if data.get('latest_stocks'):
                        print(f"   📈 最新股票数据: {len(data['latest_stocks'])} 个级别")
                        # 显示一些级别信息
                        for level, stocks in list(data['latest_stocks'].items())[:3]:
                            print(f"      - {level}: {len(stocks)} 只股票")
        except Exception as e:
            print(f"❌ {endpoint}: {e}")
            api_results[endpoint] = False
    
    print()
    return api_results

def check_javascript_syntax():
    """检查JavaScript文件语法"""
    print("🔍 检查JavaScript语法...")
    
    js_file = "show_data/static/js/charts.js"
    
    if not os.path.exists(js_file):
        print(f"❌ JavaScript文件不存在: {js_file}")
        return False
    
    try:
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查常量重复声明
        const_declarations = re.findall(r'const\s+(\w+)\s*=', content)
        
        # 统计重复的常量名
        const_counts = {}
        for const_name in const_declarations:
            const_counts[const_name] = const_counts.get(const_name, 0) + 1
        
        duplicates = {name: count for name, count in const_counts.items() if count > 1}
        
        print(f"📊 总常量声明数: {len(const_declarations)}")
        print(f"🔍 唯一常量名数: {len(const_counts)}")
        print(f"❌ 重复常量名数: {len(duplicates)}")
        
        if duplicates:
            print("\n⚠️ 发现重复的常量声明:")
            for name, count in duplicates.items():
                print(f"   - {name}: {count} 次")
                # 找到这些重复声明的位置
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if re.search(rf'const\s+{name}\s*=', line):
                        print(f"     第 {i+1} 行: {line.strip()}")
        
        # 检查函数重复定义
        function_declarations = re.findall(r'function\s+(\w+)\s*\(', content)
        func_counts = {}
        for func_name in function_declarations:
            func_counts[func_name] = func_counts.get(func_name, 0) + 1
        
        func_duplicates = {name: count for name, count in func_counts.items() if count > 1}
        
        print(f"\n📊 总函数声明数: {len(function_declarations)}")
        print(f"❌ 重复函数名数: {len(func_duplicates)}")
        
        if func_duplicates:
            print("\n⚠️ 发现重复的函数声明:")
            for name, count in func_duplicates.items():
                print(f"   - {name}: {count} 次")
        
        # 检查特定的问题模式
        assignment_to_const = re.findall(r'(\w+)\s*=.*//.*const', content)
        print(f"\n🔍 可能的常量重新赋值: {len(assignment_to_const)}")
        
        return len(duplicates) == 0 and len(func_duplicates) == 0
        
    except Exception as e:
        print(f"❌ 检查JavaScript语法失败: {e}")
        return False

def test_page_load():
    """测试页面是否能正常加载"""
    print("🔍 测试页面加载...")
    
    try:
        response = requests.get("http://localhost:8080", timeout=10)
        print(f"✅ 主页响应: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            
            # 检查关键元素
            has_pyramid_container = 'lianban-pyramid-visual' in content
            has_charts_js = 'charts.js' in content
            has_bootstrap = 'bootstrap' in content
            
            print(f"📊 包含金字塔容器: {has_pyramid_container}")
            print(f"📈 包含charts.js: {has_charts_js}")
            print(f"🎨 包含Bootstrap: {has_bootstrap}")
            
            return response.status_code == 200 and has_pyramid_container and has_charts_js
        
        return False
        
    except Exception as e:
        print(f"❌ 页面加载测试失败: {e}")
        return False

def test_javascript_functions():
    """测试JavaScript函数是否存在"""
    print("🔍 检查JavaScript函数...")
    
    js_file = "show_data/static/js/charts.js"
    
    try:
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键函数
        key_functions = [
            'updateModernPyramid',
            'loadLianbanData',
            'updateSuccessRatesDisplay',
            'calculateOverallSuccessRate'
        ]
        
        function_status = {}
        for func in key_functions:
            pattern = rf'function\s+{func}\s*\('
            exists = bool(re.search(pattern, content))
            function_status[func] = exists
            print(f"{'✅' if exists else '❌'} {func}: {'存在' if exists else '缺失'}")
        
        return all(function_status.values())
        
    except Exception as e:
        print(f"❌ 检查JavaScript函数失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 连板金字塔修复验证测试 (简化版)")
    print("=" * 60)
    
    # 测试API端点
    api_results = test_api_endpoints()
    
    # 检查JavaScript语法
    print("🔧 检查JavaScript语法...")
    js_syntax_ok = check_javascript_syntax()
    print()
    
    # 测试页面加载
    print("📄 测试页面加载...")
    page_load_ok = test_page_load()
    print()
    
    # 检查JavaScript函数
    print("⚙️ 检查JavaScript函数...")
    js_functions_ok = test_javascript_functions()
    print()
    
    # 总结结果
    print("=" * 60)
    print("📋 测试结果总结")
    print("=" * 60)
    
    api_ok = all(api_results.values())
    
    if api_ok:
        print("✅ API端点正常响应")
    else:
        print("❌ 部分API端点异常")
        for endpoint, status in api_results.items():
            if not status:
                print(f"   ❌ {endpoint}")
    
    if js_syntax_ok:
        print("✅ JavaScript语法检查通过")
    else:
        print("❌ JavaScript语法存在问题")
    
    if page_load_ok:
        print("✅ 页面加载正常")
    else:
        print("❌ 页面加载异常")
    
    if js_functions_ok:
        print("✅ JavaScript函数完整")
    else:
        print("❌ JavaScript函数缺失")
    
    # 最终评估
    all_ok = api_ok and js_syntax_ok and page_load_ok and js_functions_ok
    
    if all_ok:
        print("\n🎉 基础检查通过！建议进行浏览器测试验证")
        print("💡 提示：在浏览器中访问 http://localhost:5000 查看实际效果")
        return True
    else:
        print("\n⚠️ 发现问题，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
