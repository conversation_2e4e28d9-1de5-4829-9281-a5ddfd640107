<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>连板金字塔测试页面03</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin: 5px 0;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        #pyramid-test {
            min-height: 400px;
            border: 2px dashed #ccc;
            padding: 20px;
            margin: 10px 0;
            background-color: #fafafa;
        }
        .api-result {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin: 5px 0;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 连板金字塔功能测试页面03</h1>
        <p>此页面用于验证连板金字塔JavaScript错误修复效果</p>

        <!-- API测试 -->
        <div class="test-section">
            <div class="test-title">📡 API连接测试</div>
            <div id="api-status">正在测试API连接...</div>
            <div id="api-details" class="api-result"></div>
        </div>

        <!-- JavaScript错误检测 -->
        <div class="test-section">
            <div class="test-title">🔍 JavaScript错误检测</div>
            <div id="js-status">正在检测JavaScript错误...</div>
            <div id="js-errors" class="api-result"></div>
        </div>

        <!-- 连板金字塔测试 -->
        <div class="test-section">
            <div class="test-title">🏗️ 连板金字塔显示测试</div>
            <div id="pyramid-status">正在加载连板金字塔...</div>
            <div id="pyramid-test"></div>
        </div>

        <!-- 功能验证 -->
        <div class="test-section">
            <div class="test-title">✅ 功能验证结果</div>
            <div id="final-result">正在进行最终验证...</div>
        </div>
    </div>

    <script>
        // 错误收集
        const errors = [];
        const originalConsoleError = console.error;
        console.error = function(...args) {
            errors.push(args.join(' '));
            originalConsoleError.apply(console, args);
        };

        // 捕获全局错误
        window.addEventListener('error', function(e) {
            errors.push(`${e.message} at ${e.filename}:${e.lineno}`);
        });

        // 测试API连接
        async function testAPI() {
            const apiStatus = document.getElementById('api-status');
            const apiDetails = document.getElementById('api-details');
            
            const endpoints = [
                '/api/lianban_progress',
                '/api/market_summary',
                '/api/limit_stats'
            ];
            
            let results = [];
            let allSuccess = true;
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint);
                    const success = response.status === 200;
                    allSuccess = allSuccess && success;
                    
                    results.push(`${success ? '✅' : '❌'} ${endpoint}: ${response.status}`);
                    
                    if (success && endpoint === '/api/lianban_progress') {
                        const data = await response.json();
                        if (data.success) {
                            results.push(`   📊 历史数据: ${data.data?.length || 0} 条`);
                            results.push(`   📈 股票级别: ${Object.keys(data.latest_stocks || {}).length} 个`);
                        }
                    }
                } catch (error) {
                    allSuccess = false;
                    results.push(`❌ ${endpoint}: ${error.message}`);
                }
            }
            
            apiStatus.className = `status ${allSuccess ? 'success' : 'error'}`;
            apiStatus.textContent = allSuccess ? '✅ API连接正常' : '❌ API连接异常';
            apiDetails.textContent = results.join('\n');
            
            return allSuccess;
        }

        // 测试连板金字塔
        async function testPyramid() {
            const pyramidStatus = document.getElementById('pyramid-status');
            const pyramidTest = document.getElementById('pyramid-test');
            
            try {
                // 模拟连板金字塔加载
                pyramidTest.innerHTML = '<div id="lianban-pyramid-visual">加载中...</div>';
                
                // 获取连板数据
                const response = await fetch('/api/lianban_progress');
                if (!response.ok) {
                    throw new Error(`API响应错误: ${response.status}`);
                }
                
                const data = await response.json();
                if (!data.success) {
                    throw new Error('API返回失败状态');
                }
                
                // 模拟updateModernPyramid函数调用
                if (typeof updateModernPyramid === 'function') {
                    updateModernPyramid(data.data, data.latest_stocks);
                    
                    // 检查金字塔是否成功渲染
                    setTimeout(() => {
                        const pyramidContent = document.getElementById('lianban-pyramid-visual');
                        if (pyramidContent) {
                            const hasContent = pyramidContent.innerHTML.includes('pyramid-level') || 
                                             pyramidContent.innerHTML.length > 50;
                            
                            if (hasContent) {
                                pyramidStatus.className = 'status success';
                                pyramidStatus.textContent = '✅ 连板金字塔加载成功';
                            } else {
                                pyramidStatus.className = 'status warning';
                                pyramidStatus.textContent = '⚠️ 连板金字塔内容为空';
                            }
                        }
                    }, 1000);
                } else {
                    throw new Error('updateModernPyramid函数未定义');
                }
                
                return true;
            } catch (error) {
                pyramidStatus.className = 'status error';
                pyramidStatus.textContent = `❌ 连板金字塔加载失败: ${error.message}`;
                pyramidTest.innerHTML = `<div class="error">错误: ${error.message}</div>`;
                return false;
            }
        }

        // 检查JavaScript错误
        function checkJSErrors() {
            const jsStatus = document.getElementById('js-status');
            const jsErrors = document.getElementById('js-errors');
            
            // 检查是否有Assignment to constant variable错误
            const hasConstantError = errors.some(error => 
                error.includes('Assignment to constant variable')
            );
            
            if (errors.length === 0) {
                jsStatus.className = 'status success';
                jsStatus.textContent = '✅ 无JavaScript错误';
                jsErrors.textContent = '未检测到任何JavaScript错误';
            } else if (hasConstantError) {
                jsStatus.className = 'status error';
                jsStatus.textContent = '❌ 发现常量赋值错误';
                jsErrors.textContent = errors.join('\n');
            } else {
                jsStatus.className = 'status warning';
                jsStatus.textContent = `⚠️ 发现 ${errors.length} 个非关键错误`;
                jsErrors.textContent = errors.join('\n');
            }
            
            return !hasConstantError;
        }

        // 最终验证
        async function finalVerification() {
            const finalResult = document.getElementById('final-result');
            
            const apiOk = await testAPI();
            const pyramidOk = await testPyramid();
            
            // 等待一段时间让所有异步操作完成
            setTimeout(() => {
                const jsOk = checkJSErrors();
                
                const allOk = apiOk && pyramidOk && jsOk;
                
                if (allOk) {
                    finalResult.className = 'status success';
                    finalResult.innerHTML = `
                        <strong>🎉 修复成功！</strong><br>
                        ✅ API连接正常<br>
                        ✅ 连板金字塔功能正常<br>
                        ✅ 无关键JavaScript错误<br>
                        <br>
                        💡 连板金字塔模块已成功修复，不再出现"Assignment to constant variable"错误
                    `;
                } else {
                    finalResult.className = 'status error';
                    finalResult.innerHTML = `
                        <strong>⚠️ 仍需修复</strong><br>
                        ${apiOk ? '✅' : '❌'} API连接<br>
                        ${pyramidOk ? '✅' : '❌'} 连板金字塔<br>
                        ${jsOk ? '✅' : '❌'} JavaScript错误<br>
                    `;
                }
            }, 2000);
        }

        // 页面加载完成后开始测试
        document.addEventListener('DOMContentLoaded', function() {
            // 加载charts.js
            const script = document.createElement('script');
            script.src = '/static/js/charts.js';
            script.onload = function() {
                finalVerification();
            };
            script.onerror = function() {
                document.getElementById('js-status').className = 'status error';
                document.getElementById('js-status').textContent = '❌ charts.js加载失败';
                document.getElementById('final-result').className = 'status error';
                document.getElementById('final-result').textContent = '❌ 无法加载charts.js文件';
            };
            document.head.appendChild(script);
        });
    </script>
</body>
</html>
