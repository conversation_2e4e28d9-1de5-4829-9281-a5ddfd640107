<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>连板金字塔调试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style.css">
    <style>
        body {
            padding: 20px;
            background: #f8f9fa;
        }
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .debug-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .debug-title {
            color: #1f2937;
            font-weight: 600;
            margin-bottom: 15px;
        }
        .log-output {
            background: #1f2937;
            color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .api-response {
            background: #f8f9fa;
            border: 1px solid #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <div class="debug-card">
            <h3 class="debug-title">连板金字塔调试面板</h3>
            <p class="text-muted">实时监控JavaScript执行和API响应</p>
            
            <div class="row">
                <div class="col-md-6">
                    <!-- 连板金字塔显示区域 -->
                    <div class="pyramid-card-new" style="height: 400px;">
                        <div class="card-header py-2 text-center border-0 bg-gradient-to-r from-blue-50 to-purple-50">
                            <h6 class="mb-0 font-semibold text-gray-800">连板金字塔</h6>
                            <small class="text-gray-500" id="pyramid-date">加载中...</small>
                        </div>
                        <div class="card-body p-3">
                            <div class="pyramid-layout-fullwidth">
                                <div id="lianban-pyramid-visual" class="pyramid-container-fullwidth">
                                    <div class="text-center">
                                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                        <p class="mt-2 text-muted">正在加载连板数据...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <!-- 调试信息显示 -->
                    <div class="debug-card">
                        <h5 class="debug-title">JavaScript 执行日志</h5>
                        <div id="js-log" class="log-output">等待JavaScript执行...</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- API响应调试 -->
        <div class="debug-card">
            <h5 class="debug-title">API 响应调试</h5>
            <div class="row">
                <div class="col-md-6">
                    <h6>连板进度数据</h6>
                    <div id="api-progress" class="api-response">加载中...</div>
                </div>
                <div class="col-md-6">
                    <h6>股票数据</h6>
                    <div id="api-stocks" class="api-response">加载中...</div>
                </div>
            </div>
            <div class="mt-3">
                <button class="btn btn-primary" onclick="debugLoadData()">重新加载数据</button>
                <button class="btn btn-secondary" onclick="clearLogs()">清空日志</button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/utils.js"></script>
    
    <script>
        // 重写console.log来捕获日志
        const originalLog = console.log;
        const logOutput = [];
        
        console.log = function(...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            logOutput.push(`[${timestamp}] ${message}`);
            updateLogDisplay();
            originalLog.apply(console, args);
        };
        
        function updateLogDisplay() {
            const logElement = document.getElementById('js-log');
            logElement.textContent = logOutput.slice(-20).join('\n'); // 只显示最近20条
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLogs() {
            logOutput.length = 0;
            updateLogDisplay();
        }
        
        // 调试加载数据
        async function debugLoadData() {
            console.log('🔄 开始调试加载数据...');
            
            try {
                // 加载连板进度数据
                console.log('📡 请求连板进度数据...');
                const progressResponse = await fetch('/api/lianban_progress');
                const progressData = await progressResponse.json();
                
                console.log('📊 连板进度数据响应:', {
                    status: progressResponse.status,
                    success: progressData.success,
                    dataLength: progressData.data ? progressData.data.length : 0,
                    stocksKeys: progressData.latest_stocks ? Object.keys(progressData.latest_stocks) : null
                });
                
                // 显示API响应
                document.getElementById('api-progress').textContent = JSON.stringify(progressData, null, 2);
                document.getElementById('api-stocks').textContent = progressData.latest_stocks ? 
                    JSON.stringify(progressData.latest_stocks, null, 2) : '无股票数据';
                
                if (progressData.success && progressData.data) {
                    console.log('✅ 调用 updateModernPyramid...');
                    updateModernPyramid(progressData.data, progressData.latest_stocks);
                } else {
                    console.log('❌ API数据无效');
                }
                
            } catch (error) {
                console.log('💥 加载数据出错:', error.message);
            }
        }
        
        // 页面加载完成后自动开始调试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 调试页面加载完成');
            setTimeout(debugLoadData, 1000);
        });
    </script>
    
    <!-- 加载charts.js -->
    <script src="/static/js/charts.js"></script>
</body>
</html>
