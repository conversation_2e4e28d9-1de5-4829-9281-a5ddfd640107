#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据可视化Web应用
显示连板进度、市场概况、涨跌停统计等数据的时间趋势
"""

from flask import Flask, render_template, jsonify
import json
import os
import glob
from datetime import datetime, time
from collections import defaultdict
import re
import logging
from config import config

# 实时数据缓存目录 - 使用show_data目录下的realtime_data
REALTIME_DATA_PATH = os.path.join(os.path.dirname(__file__), 'realtime_data')
if not os.path.exists(REALTIME_DATA_PATH):
    os.makedirs(REALTIME_DATA_PATH)

def is_market_open(check_time=None):
    """
    判断是否在开盘时间
    开盘时间：9:30-11:30, 13:00-15:00
    """
    if check_time is None:
        check_time = datetime.now()

    # 获取当前时间
    current_time = check_time.time()

    # 定义开盘时间段
    morning_start = time(9, 30)   # 9:30
    morning_end = time(11, 30)    # 11:30
    afternoon_start = time(13, 0) # 13:00
    afternoon_end = time(15, 0)   # 15:00

    # 判断是否在开盘时间内
    is_morning_session = morning_start <= current_time <= morning_end
    is_afternoon_session = afternoon_start <= current_time <= afternoon_end

    return is_morning_session or is_afternoon_session

# 创建Flask应用
def create_app(config_name=None):
    app = Flask(__name__)

    # 加载配置
    config_name = config_name or os.environ.get('FLASK_CONFIG') or 'default'
    app.config.from_object(config[config_name])

    # 配置日志
    if not app.debug:
        logging.basicConfig(
            level=getattr(logging, app.config['LOG_LEVEL']),
            format='%(asctime)s %(levelname)s: %(message)s',
            handlers=[
                logging.FileHandler(app.config['LOG_FILE']),
                logging.StreamHandler()
            ]
        )

    return app

app = create_app()

# 数据文件路径
DABANKE_DATA_PATH = app.config['DABANKE_DATA_PATH']
MARKET_DATA_PATH = app.config['MARKET_DATA_PATH']

@app.route('/')
def index():
    """主页面"""
    return render_template('index.html')

@app.route('/test_data.html')
def test_data():
    """测试页面"""
    import os
    test_file_path = os.path.join(os.path.dirname(__file__), 'test_data.html')
    with open(test_file_path, 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/test_pyramid.html')
def test_pyramid():
    """连板金字塔测试页面"""
    import os
    test_file_path = os.path.join(os.path.dirname(__file__), 'test_pyramid.html')
    with open(test_file_path, 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/debug_pyramid.html')
def debug_pyramid():
    """连板金字塔调试页面"""
    import os
    debug_file_path = os.path.join(os.path.dirname(__file__), 'debug_pyramid.html')
    with open(debug_file_path, 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/test_final.html')
def test_final():
    """最终修复验证页面"""
    import os
    test_file_path = os.path.join(os.path.dirname(__file__), 'test_final.html')
    with open(test_file_path, 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/test_browser_check_03.html')
def test_browser_check_03():
    """连板金字塔浏览器测试页面03"""
    import os
    test_file_path = os.path.join(os.path.dirname(__file__), 'test_browser_check_03.html')
    with open(test_file_path, 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/test_pyramid_fix_04.html')
def test_pyramid_fix_04():
    """连板金字塔修复测试页面04"""
    import os
    test_file_path = os.path.join(os.path.dirname(__file__), 'test_pyramid_fix_04.html')
    with open(test_file_path, 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/test_market_color_05.html')
def test_market_color_05():
    """大盘颜色测试页面05"""
    import os
    test_file_path = os.path.join(os.path.dirname(__file__), 'test_market_color_05.html')
    with open(test_file_path, 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/test_pyramid_smart_06.html')
def test_pyramid_smart_06():
    """连板金字塔智能显示测试页面06"""
    import os
    test_file_path = os.path.join(os.path.dirname(__file__), 'test_pyramid_smart_06.html')
    with open(test_file_path, 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/test_space_allocation_07.html')
def test_space_allocation_07():
    """连板金字塔空间分配优化测试页面07"""
    import os
    test_file_path = os.path.join(os.path.dirname(__file__), 'test_space_allocation_07.html')
    with open(test_file_path, 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/favicon.ico')
def favicon():
    """简单的favicon处理"""
    return '', 204

@app.route('/api/lianban_progress')
def api_lianban_progress():
    """连板进度数据API"""
    try:
        result = load_lianban_progress_data()
        return jsonify({
            'success': True,
            'data': result['progress_data'],
            'latest_stocks': result['latest_stocks']
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/market_summary')
def api_market_summary():
    """市场概况数据API"""
    try:
        data = load_market_summary_data()
        return jsonify({
            'success': True,
            'data': data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/limit_stats')
def api_limit_stats():
    """涨跌停统计数据API"""
    try:
        data = load_limit_stats_data()
        return jsonify({
            'success': True,
            'data': data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 实时数据API接口
@app.route('/api/realtime/lianban_progress')
def api_realtime_lianban_progress():
    """实时连板进度数据API"""
    try:
        # 检查是否在开盘时间
        if not is_market_open():
            return jsonify({
                'success': False,
                'error': '非开盘时间，不提供实时数据',
                'market_status': 'closed'
            }), 200

        # 调用dabanke_success.py获取实时数据
        import sys
        import os
        sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

        # 确保可以导入dabanke_success
        try:
            import dabanke_success
            html_content = dabanke_success.get_dabanke_data()
        except ImportError:
            # 作为备选方案，从当前目录尝试导入
            sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            exec(open(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'dabanke_success.py')).read(), globals())
            html_content = get_dabanke_data()
        if html_content:
            # 确保extract_all_stocks_and_classify可用
            try:
                raw_data = dabanke_success.extract_all_stocks_and_classify(html_content)
            except NameError:
                # 如果之前的exec方式导入，使用全局函数
                raw_data = extract_all_stocks_and_classify(html_content)

            # 格式化数据以匹配前端期望的格式
            formatted_data = {
                'lianban_progress': raw_data.get('lianban_progress', {}),
                'stocks_by_level': raw_data.get('stocks_by_level', {}),
                'update_time': raw_data.get('update_time', ''),
                'concept_stats': raw_data.get('concept_stats', [])
            }

            # 只在开盘时间保存实时数据
            save_realtime_data('lianban', formatted_data)

            return jsonify({
                'success': True,
                'data': formatted_data,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'market_status': 'open'
            })
        else:
            return jsonify({
                'success': False,
                'error': '无法获取实时连板数据'
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/realtime/market_summary')
def api_realtime_market_summary():
    """实时市场概况数据API"""
    try:
        # 检查是否在开盘时间
        if not is_market_open():
            return jsonify({
                'success': False,
                'error': '非开盘时间，不提供实时数据',
                'market_status': 'closed'
            }), 200

        # 调用wencai_formatted.py获取实时数据
        import sys
        import os
        sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

        try:
            import wencai_formatted
            market_data = wencai_formatted.get_formatted_market_data()
        except ImportError:
            # 作为备选方案，使用新浪数据收集器
            try:
                from market_info_sina import SinaMarketDataCollector
                collector = SinaMarketDataCollector()
                market_sentiment = collector.get_market_sentiment()
                if market_sentiment:
                    market_data = {
                        'up_count': market_sentiment.get('up_count', 0),
                        'down_count': market_sentiment.get('down_count', 0),
                        'volume': market_sentiment.get('total_amount', 0),
                        'limit_up_count': market_sentiment.get('limit_up_count', 0),
                        'limit_down_count': market_sentiment.get('limit_down_count', 0),
                        'non_st_limit_up_count': market_sentiment.get('non_st_limit_up_count', 0),
                        'st_limit_up_count': market_sentiment.get('st_limit_up_count', 0),
                        'non_st_limit_down_count': market_sentiment.get('non_st_limit_down_count', 0),
                        'st_limit_down_count': market_sentiment.get('st_limit_down_count', 0),
                        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                else:
                    market_data = None
            except Exception as e:
                # 返回基础数据
                market_data = {
                    'up_count': 0,
                    'down_count': 0,
                    'volume': 0,
                    'limit_up_count': 0,
                    'limit_down_count': 0,
                    'non_st_limit_up_count': 0,
                    'st_limit_up_count': 0,
                    'non_st_limit_down_count': 0,
                    'st_limit_down_count': 0,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
        if market_data:
            # 确保数据格式正确，兼容不同的字段名
            formatted_data = {
                'up_count': market_data.get('up_count', 0),
                'down_count': market_data.get('down_count', 0),
                'flat_count': market_data.get('flat_count', 0),
                'volume': market_data.get('volume', 0),
                'total_amount': market_data.get('volume', 0),  # 兼容字段
                'limit_up_count': market_data.get('limit_up_count', 0),
                'limit_down_count': market_data.get('limit_down_count', 0),
                'limit_up_total': market_data.get('limit_up_count', 0),  # 兼容字段
                'limit_down_total': market_data.get('limit_down_count', 0),  # 兼容字段
                'non_st_limit_up_count': market_data.get('non_st_limit_up_count', 0),
                'st_limit_up_count': market_data.get('st_limit_up_count', 0),
                'non_st_limit_down_count': market_data.get('non_st_limit_down_count', 0),
                'st_limit_down_count': market_data.get('st_limit_down_count', 0),
                'timestamp': market_data.get('timestamp', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            }

            # 只在开盘时间保存实时数据
            save_realtime_data('market', formatted_data)

            return jsonify({
                'success': True,
                'data': formatted_data,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'market_status': 'open'
            })
        else:
            return jsonify({
                'success': False,
                'error': '无法获取实时市场数据'
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/realtime/history/<data_type>')
def api_realtime_history(data_type):
    """获取当天实时数据历史"""
    try:
        history_data = load_realtime_data(data_type)
        return jsonify({
            'success': True,
            'data': history_data,
            'count': len(history_data)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 实时数据保存和加载函数
def save_realtime_data(data_type, data):
    """保存实时数据到文件（仅在开盘时间）"""
    try:
        # 检查是否在开盘时间
        if not is_market_open():
            print(f"⏰ 非开盘时间，跳过保存实时数据: {data_type}")
            return False

        today = datetime.now().strftime('%Y-%m-%d')
        filename = f"realtime_{data_type}_{today}.json"
        filepath = os.path.join(REALTIME_DATA_PATH, filename)

        print(f"📊 保存实时数据: {data_type} -> {filepath}")
        print(f"📊 数据内容: {data}")

        # 读取现有数据
        existing_data = []
        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
            print(f"📊 现有数据点数量: {len(existing_data)}")

        # 添加新数据点
        timestamp = datetime.now().strftime('%H:%M')
        new_entry = {
            'time': timestamp,
            'data': data,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        existing_data.append(new_entry)

        # 保持最近100个数据点
        if len(existing_data) > 100:
            existing_data = existing_data[-100:]

        # 保存到文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(existing_data, f, ensure_ascii=False, indent=2)

        print(f"✅ 实时数据保存成功，总数据点: {len(existing_data)}")
        return True
    except Exception as e:
        print(f"❌ 保存实时数据失败: {e}")
        return False

def load_realtime_data(data_type):
    """加载当天的实时数据"""
    try:
        today = datetime.now().strftime('%Y-%m-%d')
        filename = f"realtime_{data_type}_{today}.json"
        filepath = os.path.join(REALTIME_DATA_PATH, filename)

        print(f"📈 加载实时数据: {data_type} <- {filepath}")

        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"✅ 实时数据加载成功，数据点数量: {len(data)}")
            return data
        else:
            print(f"⚠️ 实时数据文件不存在: {filepath}")
            return []
    except Exception as e:
        print(f"❌ 加载实时数据失败: {e}")
        return []

def load_lianban_progress_data():
    """加载连板进度数据 - 最近20天"""
    data = []
    latest_stocks = None
    pattern = os.path.join(DABANKE_DATA_PATH, 'dabanke_data_*.json')
    files = sorted(glob.glob(pattern))

    # 只取最近30天的数据
    files = files[-30:] if len(files) > 30 else files

    for file_path in files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = json.load(f)

            # 从文件名提取日期
            filename = os.path.basename(file_path)
            date_match = re.search(r'(\d{4}-\d{2}-\d{2})', filename)
            if not date_match:
                continue

            date = date_match.group(1)

            if 'lianban_progress' in content:
                progress_data = content['lianban_progress']

                # 解析连板进度数据
                parsed_data = {}
                for level, progress_str in progress_data.items():
                    # 解析 "18/59=31%" 格式
                    match = re.search(r'(\d+)/(\d+)=(\d+)%', progress_str)
                    if match:
                        success = int(match.group(1))
                        total = int(match.group(2))
                        percentage = int(match.group(3))
                        parsed_data[level] = {
                            'success': success,
                            'total': total,
                            'percentage': percentage
                        }

                data.append({
                    'date': date,
                    'progress': parsed_data
                })

                # 保存最新一天的个股数据
                if 'stocks_by_level' in content:
                    latest_stocks = content['stocks_by_level']

        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            continue

    return {
        'progress_data': data,
        'latest_stocks': latest_stocks
    }

def load_market_summary_data():
    """加载市场概况数据 - 最近30天"""
    data = []
    pattern = os.path.join(MARKET_DATA_PATH, 'market_data_*.json')
    files = sorted(glob.glob(pattern))

    # 只取最近30天的数据
    files = files[-30:] if len(files) > 30 else files

    for file_path in files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = json.load(f)

            if 'date' in content and 'market_summary' in content:
                date = content['date']
                summary = content['market_summary']
                limit_stats = content.get('limit_stats', {})

                # 获取涨跌数据
                up_count = summary.get('up_count', 0)
                down_count = summary.get('down_count', 0)

                # 计算平盘数量（估算总股票数约为5000）
                estimated_total = 5000
                flat_count = max(0, estimated_total - up_count - down_count)

                data.append({
                    'date': date,
                    'rise_count': up_count,
                    'fall_count': down_count,
                    'flat_count': flat_count,
                    'limit_up': limit_stats.get('limit_up', {}).get('total', 0),
                    'limit_down': limit_stats.get('limit_down', {}).get('total', 0),
                    'limit_up_total': limit_stats.get('limit_up', {}).get('total', 0),
                    'limit_down_total': limit_stats.get('limit_down', {}).get('total', 0),
                    'volume': summary.get('total_amount', 0),
                    'total_amount': summary.get('total_amount', 0),
                    'volume_formatted': summary.get('total_amount_formatted', '0')
                })

        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            continue

    return data

def load_limit_stats_data():
    """加载涨跌停统计数据 - 最近30天"""
    data = []
    pattern = os.path.join(MARKET_DATA_PATH, 'market_data_*.json')
    files = sorted(glob.glob(pattern))

    # 只取最近30天的数据
    files = files[-30:] if len(files) > 30 else files

    for file_path in files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = json.load(f)

            if 'date' in content and 'limit_stats' in content:
                date = content['date']
                stats = content['limit_stats']

                limit_up = stats.get('limit_up', {})
                limit_down = stats.get('limit_down', {})

                data.append({
                    'date': date,
                    'limit_up': limit_up.get('total', 0),
                    'limit_down': limit_down.get('total', 0),
                    'limit_up_total': limit_up.get('total', 0),
                    'limit_up_non_st': limit_up.get('non_st', 0),
                    'limit_up_st': limit_up.get('st', 0),
                    'limit_down_total': limit_down.get('total', 0),
                    'limit_down_non_st': limit_down.get('non_st', 0),
                    'limit_down_st': limit_down.get('st', 0)
                })

        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            continue

    return data

if __name__ == '__main__':
    print("🚀 启动股票数据可视化Web服务器")
    print("=" * 50)
    print(f"📊 访问地址: http://localhost:8080")
    print(f"🧪 测试页面: http://localhost:8080/test_data.html")
    print("=" * 50)

    app.run(
        debug=True,
        host='0.0.0.0',
        port=8080,
        threaded=True
    )
