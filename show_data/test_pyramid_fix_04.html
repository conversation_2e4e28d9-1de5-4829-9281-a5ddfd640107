<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>连板金字塔修复测试04</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style.css">
    <style>
        body {
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-title {
            color: #1f2937;
            font-weight: 600;
            margin-bottom: 15px;
        }
        .status {
            padding: 8px 12px;
            border-radius: 6px;
            margin: 5px 0;
            font-weight: 500;
        }
        .status.success { background: #d1fae5; color: #065f46; }
        .status.error { background: #fee2e2; color: #991b1b; }
        .status.warning { background: #fef3c7; color: #92400e; }
        .status.info { background: #dbeafe; color: #1e40af; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-card">
            <h3 class="test-title">连板金字塔 gridCols 修复测试04</h3>
            <p>测试修复 "gridCols is not defined" 错误</p>
            
            <div id="test-status" class="status info">🔄 开始测试...</div>
            <div id="api-status" class="status info">📡 等待API测试...</div>
            <div id="pyramid-status" class="status info">🏗️ 等待金字塔渲染...</div>
            <div id="error-status" class="status info">🐛 等待错误检查...</div>
        </div>
        
        <!-- 金字塔显示区域 -->
        <div class="test-card">
            <h5>连板金字塔显示</h5>
            <div class="pyramid-card-new" style="height: 500px;">
                <div class="card-header py-2 text-center border-0 bg-gradient-to-r from-blue-50 to-purple-50">
                    <h6 class="mb-0 font-semibold text-gray-800">连板金字塔</h6>
                    <small class="text-gray-500" id="pyramid-date">加载中...</small>
                </div>
                <div class="card-body p-3">
                    <div class="pyramid-layout-fullwidth">
                        <div id="lianban-pyramid-visual" class="pyramid-container-fullwidth">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2 text-muted">正在加载连板数据...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 控制台日志显示 -->
        <div class="test-card">
            <h5>控制台日志</h5>
            <div id="console-logs" style="background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace; font-size: 0.85rem; max-height: 300px; overflow-y: auto;">
                <div>等待日志输出...</div>
            </div>
        </div>
    </div>

    <!-- 引入必要的JavaScript文件 -->
    <script src="/static/js/charts.js"></script>
    
    <script>
        // 捕获控制台日志
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        const logsContainer = document.getElementById('console-logs');
        
        function addLogToDisplay(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.createElement('div');
            logDiv.style.marginBottom = '5px';
            logDiv.style.color = type === 'error' ? '#dc2626' : (type === 'warn' ? '#d97706' : '#374151');
            logDiv.innerHTML = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            logsContainer.appendChild(logDiv);
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLogToDisplay('log', args.join(' '));
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addLogToDisplay('error', args.join(' '));
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addLogToDisplay('warn', args.join(' '));
        };
        
        // 捕获JavaScript错误
        window.addEventListener('error', function(event) {
            console.error('JavaScript错误:', event.error.message);
            const errorStatus = document.getElementById('error-status');
            errorStatus.className = 'status error';
            errorStatus.textContent = `❌ JavaScript错误: ${event.error.message}`;
        });
        
        // 开始测试
        async function runTest() {
            const testStatus = document.getElementById('test-status');
            const apiStatus = document.getElementById('api-status');
            const pyramidStatus = document.getElementById('pyramid-status');
            const errorStatus = document.getElementById('error-status');
            
            try {
                testStatus.className = 'status info';
                testStatus.textContent = '🔄 开始测试连板金字塔修复...';
                
                // 测试API调用
                apiStatus.className = 'status info';
                apiStatus.textContent = '📡 正在调用连板数据API...';
                
                const response = await fetch('/api/lianban_progress');
                const data = await response.json();
                
                console.log('API响应数据:', data);
                
                if (data.success) {
                    apiStatus.className = 'status success';
                    apiStatus.textContent = '✅ API调用成功';
                    
                    // 测试金字塔渲染
                    pyramidStatus.className = 'status info';
                    pyramidStatus.textContent = '🏗️ 正在渲染连板金字塔...';
                    
                    // 模拟updateModernPyramid函数调用
                    if (typeof updateModernPyramid === 'function') {
                        updateModernPyramid(data.data, data.latest_stocks);
                        
                        // 检查金字塔是否成功渲染
                        setTimeout(() => {
                            const pyramidContent = document.getElementById('lianban-pyramid-visual');
                            if (pyramidContent) {
                                const hasContent = pyramidContent.innerHTML.includes('lianban-level-row') || 
                                                 pyramidContent.innerHTML.length > 100;
                                
                                if (hasContent) {
                                    pyramidStatus.className = 'status success';
                                    pyramidStatus.textContent = '✅ 连板金字塔渲染成功';
                                    
                                    // 检查是否有gridCols错误
                                    if (!pyramidContent.innerHTML.includes('gridCols is not defined')) {
                                        errorStatus.className = 'status success';
                                        errorStatus.textContent = '✅ 没有发现gridCols错误，修复成功！';
                                        
                                        testStatus.className = 'status success';
                                        testStatus.textContent = '🎉 所有测试通过，gridCols错误已修复！';
                                    } else {
                                        errorStatus.className = 'status error';
                                        errorStatus.textContent = '❌ 仍然存在gridCols错误';
                                    }
                                } else {
                                    pyramidStatus.className = 'status warning';
                                    pyramidStatus.textContent = '⚠️ 连板金字塔内容为空';
                                }
                            }
                        }, 1000);
                    } else {
                        throw new Error('updateModernPyramid函数未定义');
                    }
                } else {
                    throw new Error(data.error || '未知API错误');
                }
            } catch (error) {
                console.error('测试失败:', error);
                testStatus.className = 'status error';
                testStatus.textContent = `❌ 测试失败: ${error.message}`;
                
                apiStatus.className = 'status error';
                apiStatus.textContent = `❌ API调用失败: ${error.message}`;
            }
        }
        
        // 页面加载完成后开始测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始测试...');
            setTimeout(runTest, 500);
        });
    </script>
</body>
</html>
