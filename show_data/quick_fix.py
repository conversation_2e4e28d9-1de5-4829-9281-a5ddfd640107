#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复实时数据显示问题
"""

import os
import sys
import json
from datetime import datetime, timedelta

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def is_market_open_time(hour, minute):
    """判断指定时间是否在开盘时间"""
    total_minutes = hour * 60 + minute

    # 开盘时间段：9:30-11:30 (570-690分钟), 13:00-15:00 (780-900分钟)
    morning_start = 9 * 60 + 30  # 570
    morning_end = 11 * 60 + 30   # 690
    afternoon_start = 13 * 60    # 780
    afternoon_end = 15 * 60      # 900

    return (morning_start <= total_minutes <= morning_end) or \
           (afternoon_start <= total_minutes <= afternoon_end)

def create_sample_realtime_data():
    """创建示例实时数据（仅开盘时间）"""
    print("🔧 创建示例实时数据（仅开盘时间）...")

    # 确保实时数据目录存在
    realtime_dir = os.path.join(os.path.dirname(__file__), 'realtime_data')
    if not os.path.exists(realtime_dir):
        os.makedirs(realtime_dir)
        print(f"✅ 创建实时数据目录: {realtime_dir}")

    # 生成今天的实时市场数据
    today = datetime.now().strftime('%Y-%m-%d')
    filename = f"realtime_market_{today}.json"
    filepath = os.path.join(realtime_dir, filename)

    realtime_data = []

    # 生成上午开盘时间的数据 (9:30-11:30)
    print("📊 生成上午开盘时间数据...")
    for hour in range(9, 12):
        start_minute = 30 if hour == 9 else 0
        end_minute = 30 if hour == 11 else 59

        for minute in range(start_minute, end_minute + 1, 5):  # 每5分钟一个数据点
            if hour == 11 and minute > 30:  # 11:30后停止
                break

            if is_market_open_time(hour, minute):
                time_str = f"{hour:02d}:{minute:02d}"

                # 模拟市场数据变化
                time_factor = (hour - 9) * 60 + minute - 30  # 从9:30开始的分钟数

                base_up = 1200
                base_down = 3500
                up_count = int(base_up + time_factor * 8)
                down_count = int(base_down - time_factor * 5)

                data_point = {
                    'time': time_str,
                    'data': {
                        'up_count': up_count,
                        'down_count': down_count,
                        'flat_count': 300,
                        'volume': 8000000000000 + int(time_factor * 50000000000),
                        'total_amount': 8000000000000 + int(time_factor * 50000000000),
                        'limit_up_count': 25 + int(time_factor / 10),
                        'limit_down_count': max(1, 8 - int(time_factor / 15)),
                        'limit_up_total': 25 + int(time_factor / 10),
                        'limit_down_total': max(1, 8 - int(time_factor / 15)),
                        'timestamp': f'2025-08-14 {time_str}:00'
                    },
                    'timestamp': f'2025-08-14 {time_str}:00'
                }

                realtime_data.append(data_point)

    # 生成下午开盘时间的数据 (13:00-15:00)
    print("📊 生成下午开盘时间数据...")
    for hour in range(13, 16):
        end_minute = 0 if hour == 15 else 59

        for minute in range(0, end_minute + 1, 5):  # 每5分钟一个数据点
            if hour == 15 and minute > 0:  # 15:00后停止
                break

            if is_market_open_time(hour, minute):
                time_str = f"{hour:02d}:{minute:02d}"

                # 模拟市场数据变化（下午继续上午的趋势）
                time_factor = 120 + (hour - 13) * 60 + minute  # 从上午结束后继续计算

                base_up = 1200
                base_down = 3500
                up_count = int(base_up + time_factor * 8)
                down_count = int(base_down - time_factor * 5)

                data_point = {
                    'time': time_str,
                    'data': {
                        'up_count': up_count,
                        'down_count': down_count,
                        'flat_count': 300,
                        'volume': 8000000000000 + int(time_factor * 50000000000),
                        'total_amount': 8000000000000 + int(time_factor * 50000000000),
                        'limit_up_count': 25 + int(time_factor / 10),
                        'limit_down_count': max(1, 8 - int(time_factor / 15)),
                        'limit_up_total': 25 + int(time_factor / 10),
                        'limit_down_total': max(1, 8 - int(time_factor / 15)),
                        'timestamp': f'2025-08-14 {time_str}:00'
                    },
                    'timestamp': f'2025-08-14 {time_str}:00'
                }

                realtime_data.append(data_point)

    # 保存数据
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(realtime_data, f, ensure_ascii=False, indent=2)

    print(f"✅ 创建实时市场数据: {filepath}")
    print(f"   数据点数量: {len(realtime_data)}")
    if realtime_data:
        print(f"   时间范围: {realtime_data[0]['time']} - {realtime_data[-1]['time']}")
        print(f"   开盘时间数据点: {len([d for d in realtime_data if is_market_open_time(int(d['time'].split(':')[0]), int(d['time'].split(':')[1]))])}")

    return filepath

def verify_data_structure():
    """验证数据结构"""
    print("\n🔍 验证数据结构...")
    
    realtime_dir = os.path.join(os.path.dirname(__file__), 'realtime_data')
    today = datetime.now().strftime('%Y-%m-%d')
    filename = f"realtime_market_{today}.json"
    filepath = os.path.join(realtime_dir, filename)
    
    if not os.path.exists(filepath):
        print(f"❌ 文件不存在: {filepath}")
        return False
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not data:
            print("❌ 数据为空")
            return False
        
        # 检查数据结构
        sample = data[0]
        required_fields = ['time', 'data', 'timestamp']
        
        for field in required_fields:
            if field not in sample:
                print(f"❌ 缺少字段: {field}")
                return False
        
        # 检查数据字段
        data_fields = ['up_count', 'down_count', 'volume', 'limit_up_count', 'limit_down_count']
        for field in data_fields:
            if field not in sample['data']:
                print(f"❌ 数据中缺少字段: {field}")
                return False
        
        print("✅ 数据结构验证通过")
        print(f"   样本数据: {sample}")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def test_frontend_compatibility():
    """测试前端兼容性"""
    print("\n🎯 测试前端兼容性...")
    
    # 模拟前端数据处理逻辑
    realtime_dir = os.path.join(os.path.dirname(__file__), 'realtime_data')
    today = datetime.now().strftime('%Y-%m-%d')
    filename = f"realtime_market_{today}.json"
    filepath = os.path.join(realtime_dir, filename)
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 模拟前端处理
        labels = [item['time'] for item in data]
        up_data = []
        down_data = []
        
        for item in data:
            # 兼容不同的数据结构（模拟前端逻辑）
            if 'data' in item:
                up_count = item['data'].get('up_count', 0)
                down_count = item['data'].get('down_count', 0)
            else:
                up_count = item.get('up_count', 0)
                down_count = item.get('down_count', 0)
            
            up_data.append(up_count)
            down_data.append(down_count)
        
        print(f"✅ 前端兼容性测试通过")
        print(f"   标签数量: {len(labels)}")
        print(f"   上涨数据点: {len(up_data)}, 范围: {min(up_data)}-{max(up_data)}")
        print(f"   下跌数据点: {len(down_data)}, 范围: {min(down_data)}-{max(down_data)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 前端兼容性测试失败: {e}")
        return False

if __name__ == '__main__':
    print("🚀 快速修复实时数据显示")
    print("=" * 50)
    
    # 创建示例数据
    filepath = create_sample_realtime_data()
    
    # 验证数据结构
    if verify_data_structure():
        # 测试前端兼容性
        if test_frontend_compatibility():
            print("\n✨ 修复完成！")
            print("💡 现在启动服务器并刷新页面，应该能看到实时数据了")
            print("🌐 访问: http://localhost:5000")
        else:
            print("\n❌ 前端兼容性测试失败")
    else:
        print("\n❌ 数据结构验证失败")
    
    print("\n📋 下一步:")
    print("1. 运行: python start_server.py")
    print("2. 访问: http://localhost:5000")
    print("3. 查看浏览器控制台日志")
    print("4. 检查实时数据是否显示（虚线）")
