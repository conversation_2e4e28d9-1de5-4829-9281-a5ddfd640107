<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>连板金字塔紧急修复测试 09</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="static/css/style.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .emergency-alert {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .fix-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-left: 10px;
        }
        
        .fix-success {
            background: #d4edda;
            color: #155724;
        }
        
        .fix-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .pyramid-test-container {
            height: 600px;
            border: 2px solid #007bff;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">🚨 连板金字塔紧急修复测试 09</h1>
        
        <div class="emergency-alert">
            <h5>🔧 紧急修复内容</h5>
            <ul>
                <li><strong>首板股票显示问题</strong>：修复首板级别股票不显示的问题</li>
                <li><strong>标签对齐问题</strong>：修复连板级别标签与胜率标签的对齐问题</li>
                <li><strong>高度分配优化</strong>：调整空间分配算法，确保足够的显示空间</li>
                <li><strong>布局算法改进</strong>：改进股票显示的布局计算逻辑</li>
            </ul>
        </div>

        <!-- 修复状态检查 -->
        <div class="test-card">
            <h5>🔍 修复状态检查</h5>
            <div class="row">
                <div class="col-md-6">
                    <div>首板股票显示 <span id="fix-status-1" class="fix-status fix-warning">检查中...</span></div>
                    <div>标签对齐效果 <span id="fix-status-2" class="fix-status fix-warning">检查中...</span></div>
                    <div>高度分配合理性 <span id="fix-status-3" class="fix-status fix-warning">检查中...</span></div>
                </div>
                <div class="col-md-6">
                    <div>股票数量显示 <span id="fix-status-4" class="fix-status fix-warning">检查中...</span></div>
                    <div>布局响应性 <span id="fix-status-5" class="fix-status fix-warning">检查中...</span></div>
                    <div>整体视觉效果 <span id="fix-status-6" class="fix-status fix-warning">检查中...</span></div>
                </div>
            </div>
        </div>

        <!-- 测试场景选择 -->
        <div class="test-card">
            <h5>📊 测试场景</h5>
            <div class="btn-group mb-3" role="group">
                <button type="button" class="btn btn-outline-primary active" onclick="loadTestScenario('critical')">关键问题测试</button>
                <button type="button" class="btn btn-outline-primary" onclick="loadTestScenario('alignment')">对齐测试</button>
                <button type="button" class="btn btn-outline-primary" onclick="loadTestScenario('large_data')">大数据测试</button>
            </div>
            
            <div id="scenario-description" class="alert alert-info">
                <strong>关键问题测试</strong>：重点测试首板股票显示和标签对齐问题
            </div>
        </div>

        <!-- 金字塔显示区域 -->
        <div class="test-card">
            <h5>🏗️ 连板金字塔显示</h5>
            <div class="pyramid-test-container">
                <div class="pyramid-card-new" style="height: 100%;">
                    <div class="card-header py-2 text-center border-0 bg-gradient-to-r from-blue-50 to-purple-50">
                        <h6 class="mb-0 font-semibold text-gray-800">连板金字塔</h6>
                        <small class="text-gray-500" id="pyramid-date">紧急修复版本</small>
                    </div>
                    <div class="card-body p-3">
                        <div class="pyramid-layout-fullwidth">
                            <div id="lianban-pyramid-visual" class="pyramid-container-fullwidth">
                                <div class="text-center">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2 text-muted">正在加载测试数据...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="debug-info" id="debug-info">
                <strong>调试信息：</strong><br>
                等待数据加载...
            </div>
        </div>

        <!-- 修复验证结果 -->
        <div class="test-card">
            <h5>✅ 修复验证结果</h5>
            <div id="verification-results">
                <div class="text-muted">等待测试完成...</div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="static/js/charts.js"></script>
    <script>
        // 测试场景数据
        const testScenarios = {
            critical: {
                name: '关键问题测试',
                description: '重点测试首板股票显示和标签对齐问题',
                data: {
                    '首板': Array.from({length: 35}, (_, i) => ({
                        code: `SB${String(i+1).padStart(3, '0')}`,
                        name: `首板股票${i+1}`,
                        change_percent: (Math.random() * 20 - 10).toFixed(2),
                        status: Math.random() > 0.7 ? '成' : (Math.random() > 0.5 ? '炸' : '败'),
                        industry: '测试行业'
                    })),
                    '1进2': Array.from({length: 22}, (_, i) => ({
                        code: `12${String(i+1).padStart(3, '0')}`,
                        name: `1进2股票${i+1}`,
                        change_percent: (Math.random() * 15 - 5).toFixed(2),
                        status: Math.random() > 0.6 ? '成' : (Math.random() > 0.5 ? '炸' : '败'),
                        industry: '测试行业'
                    })),
                    '2进3': Array.from({length: 15}, (_, i) => ({
                        code: `23${String(i+1).padStart(3, '0')}`,
                        name: `2进3股票${i+1}`,
                        change_percent: (Math.random() * 12 - 3).toFixed(2),
                        status: Math.random() > 0.5 ? '成' : (Math.random() > 0.5 ? '炸' : '败'),
                        industry: '测试行业'
                    })),
                    '3进4': Array.from({length: 8}, (_, i) => ({
                        code: `34${String(i+1).padStart(3, '0')}`,
                        name: `3进4股票${i+1}`,
                        change_percent: (Math.random() * 10 - 2).toFixed(2),
                        status: Math.random() > 0.4 ? '成' : (Math.random() > 0.5 ? '炸' : '败'),
                        industry: '测试行业'
                    })),
                    '4进5': Array.from({length: 3}, (_, i) => ({
                        code: `45${String(i+1).padStart(3, '0')}`,
                        name: `4进5股票${i+1}`,
                        change_percent: (Math.random() * 8).toFixed(2),
                        status: Math.random() > 0.3 ? '成' : (Math.random() > 0.5 ? '炸' : '败'),
                        industry: '测试行业'
                    }))
                }
            },
            alignment: {
                name: '对齐测试',
                description: '测试不同级别标签和胜率的对齐效果',
                data: {
                    '首板': Array.from({length: 5}, (_, i) => ({
                        code: `SB${String(i+1).padStart(3, '0')}`,
                        name: `首板${i+1}`,
                        change_percent: (Math.random() * 20 - 10).toFixed(2),
                        status: '成',
                        industry: '测试'
                    })),
                    '1进2': Array.from({length: 5}, (_, i) => ({
                        code: `12${String(i+1).padStart(3, '0')}`,
                        name: `1进2股票${i+1}`,
                        change_percent: (Math.random() * 15 - 5).toFixed(2),
                        status: '成',
                        industry: '测试'
                    })),
                    '2进3': Array.from({length: 5}, (_, i) => ({
                        code: `23${String(i+1).padStart(3, '0')}`,
                        name: `2进3股票${i+1}`,
                        change_percent: (Math.random() * 12 - 3).toFixed(2),
                        status: '成',
                        industry: '测试'
                    })),
                    '3进4': Array.from({length: 5}, (_, i) => ({
                        code: `34${String(i+1).padStart(3, '0')}`,
                        name: `3进4股票${i+1}`,
                        change_percent: (Math.random() * 10 - 2).toFixed(2),
                        status: '成',
                        industry: '测试'
                    })),
                    '4进5': Array.from({length: 5}, (_, i) => ({
                        code: `45${String(i+1).padStart(3, '0')}`,
                        name: `4进5股票${i+1}`,
                        change_percent: (Math.random() * 8).toFixed(2),
                        status: '成',
                        industry: '测试'
                    }))
                }
            },
            large_data: {
                name: '大数据测试',
                description: '测试大量股票数据的显示效果',
                data: {
                    '首板': Array.from({length: 60}, (_, i) => ({
                        code: `SB${String(i+1).padStart(3, '0')}`,
                        name: `首板股票${i+1}`,
                        change_percent: (Math.random() * 20 - 10).toFixed(2),
                        status: Math.random() > 0.7 ? '成' : (Math.random() > 0.5 ? '炸' : '败'),
                        industry: '测试行业'
                    })),
                    '1进2': Array.from({length: 40}, (_, i) => ({
                        code: `12${String(i+1).padStart(3, '0')}`,
                        name: `1进2股票${i+1}`,
                        change_percent: (Math.random() * 15 - 5).toFixed(2),
                        status: Math.random() > 0.6 ? '成' : (Math.random() > 0.5 ? '炸' : '败'),
                        industry: '测试行业'
                    }))
                }
            }
        };

        // 当前测试场景
        let currentScenario = 'critical';

        // 加载测试场景
        function loadTestScenario(scenarioKey) {
            currentScenario = scenarioKey;
            const scenario = testScenarios[scenarioKey];
            
            // 更新按钮状态
            document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // 更新描述
            document.getElementById('scenario-description').innerHTML = 
                `<strong>${scenario.name}</strong>：${scenario.description}`;
            
            // 重置状态
            resetFixStatus();
            
            // 加载测试数据
            loadTestData(scenario.data);
            
            updateDebugInfo(`切换到测试场景: ${scenario.name}`);
        }

        // 重置修复状态
        function resetFixStatus() {
            for (let i = 1; i <= 6; i++) {
                const statusEl = document.getElementById(`fix-status-${i}`);
                statusEl.className = 'fix-status fix-warning';
                statusEl.textContent = '检查中...';
            }
        }

        // 加载测试数据
        function loadTestData(stocksData) {
            // 生成进度数据
            const progressData = {};
            Object.keys(stocksData).forEach(level => {
                const stocks = stocksData[level];
                const successCount = stocks.filter(s => s.status === '成').length;
                const totalCount = stocks.length;
                progressData[level] = {
                    success: successCount,
                    total: totalCount,
                    percentage: totalCount > 0 ? Math.round((successCount / totalCount) * 100) : 0
                };
            });
            
            updateDebugInfo(`开始加载数据，共 ${Object.keys(stocksData).length} 个级别`);
            
            // 更新金字塔显示
            updateModernPyramid(progressData, stocksData);
            
            // 延迟检查修复效果
            setTimeout(() => {
                checkFixStatus(stocksData, progressData);
            }, 1000);
        }

        // 检查修复状态
        function checkFixStatus(stocksData, progressData) {
            const results = [];
            
            // 检查首板股票显示
            const pyramidContent = document.getElementById('lianban-pyramid-visual').innerHTML;
            const hasFirstBoardStocks = pyramidContent.includes('首板') && pyramidContent.includes('stock-item');
            updateFixStatus(1, hasFirstBoardStocks, '首板股票显示');
            results.push(`首板股票显示: ${hasFirstBoardStocks ? '✅ 正常' : '❌ 异常'}`);
            
            // 检查标签对齐
            const hasLevelInfo = pyramidContent.includes('level-info');
            updateFixStatus(2, hasLevelInfo, '标签对齐效果');
            results.push(`标签对齐: ${hasLevelInfo ? '✅ 正常' : '❌ 异常'}`);
            
            // 检查高度分配
            const levelRows = document.querySelectorAll('.lianban-level-row');
            const hasReasonableHeights = levelRows.length > 0;
            updateFixStatus(3, hasReasonableHeights, '高度分配合理性');
            results.push(`高度分配: ${hasReasonableHeights ? '✅ 正常' : '❌ 异常'}`);
            
            // 检查股票数量显示
            const stockItems = document.querySelectorAll('.stock-item');
            const hasStockItems = stockItems.length > 0;
            updateFixStatus(4, hasStockItems, '股票数量显示');
            results.push(`股票显示: ${hasStockItems ? '✅ 正常' : '❌ 异常'} (${stockItems.length}个)`);
            
            // 检查布局响应性
            const hasGridLayout = pyramidContent.includes('grid-template-columns');
            updateFixStatus(5, hasGridLayout, '布局响应性');
            results.push(`布局响应: ${hasGridLayout ? '✅ 正常' : '❌ 异常'}`);
            
            // 检查整体视觉效果
            const hasOverallStats = pyramidContent.includes('overall-success-rate');
            updateFixStatus(6, hasOverallStats, '整体视觉效果');
            results.push(`视觉效果: ${hasOverallStats ? '✅ 正常' : '❌ 异常'}`);
            
            // 更新验证结果
            document.getElementById('verification-results').innerHTML = 
                '<div class="small">' + results.join('<br>') + '</div>';
            
            updateDebugInfo('修复状态检查完成');
        }

        // 更新修复状态
        function updateFixStatus(index, success, description) {
            const statusEl = document.getElementById(`fix-status-${index}`);
            statusEl.className = `fix-status ${success ? 'fix-success' : 'fix-warning'}`;
            statusEl.textContent = success ? '✅ 正常' : '❌ 异常';
        }

        // 更新调试信息
        function updateDebugInfo(message) {
            const debugEl = document.getElementById('debug-info');
            const timestamp = new Date().toLocaleTimeString();
            debugEl.innerHTML += `<br>[${timestamp}] ${message}`;
            debugEl.scrollTop = debugEl.scrollHeight;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateDebugInfo('紧急修复测试页面初始化完成');
            loadTestScenario('critical');
        });
    </script>
</body>
</html>
