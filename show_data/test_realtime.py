#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试实时数据生成和保存
"""

import os
import sys
import json
import requests
from datetime import datetime

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def test_realtime_apis():
    """测试实时数据API"""
    base_url = "http://localhost:5000"
    
    print("🧪 测试实时数据API")
    print("=" * 50)
    
    # 测试实时市场数据
    try:
        print("📊 测试实时市场数据API...")
        response = requests.get(f"{base_url}/api/realtime/market_summary", timeout=10)
        result = response.json()
        
        if result.get('success'):
            print("✅ 实时市场数据API正常")
            print(f"   数据: {result['data']}")
        else:
            print(f"❌ 实时市场数据API失败: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ 实时市场数据API异常: {e}")
    
    print()
    
    # 测试实时数据历史
    try:
        print("📈 测试实时数据历史API...")
        response = requests.get(f"{base_url}/api/realtime/history/market", timeout=10)
        result = response.json()
        
        if result.get('success'):
            print(f"✅ 实时数据历史API正常，数据点数量: {len(result['data'])}")
            if result['data']:
                print(f"   最新数据: {result['data'][-1]}")
        else:
            print(f"❌ 实时数据历史API失败: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ 实时数据历史API异常: {e}")

def generate_test_data():
    """生成测试实时数据"""
    print("\n🔧 生成测试实时数据")
    print("=" * 50)
    
    # 导入app模块
    from app import save_realtime_data
    
    # 生成几个测试数据点
    test_times = ['10:30', '10:35', '10:40', '10:45']
    
    for i, time_str in enumerate(test_times):
        test_data = {
            'up_count': 1500 + i * 50,
            'down_count': 3000 - i * 30,
            'flat_count': 500,
            'volume': 8000000000000 + i * 1000000000000,
            'total_amount': 8000000000000 + i * 1000000000000,
            'limit_up_count': 30 + i * 2,
            'limit_down_count': 5 - i,
            'limit_up_total': 30 + i * 2,
            'limit_down_total': 5 - i,
            'timestamp': f'2025-08-14 {time_str}:00'
        }
        
        success = save_realtime_data('market', test_data)
        if success:
            print(f"✅ 测试数据 {time_str} 保存成功")
        else:
            print(f"❌ 测试数据 {time_str} 保存失败")

def check_realtime_files():
    """检查实时数据文件"""
    print("\n📁 检查实时数据文件")
    print("=" * 50)
    
    realtime_dir = os.path.join(os.path.dirname(__file__), 'realtime_data')
    
    if not os.path.exists(realtime_dir):
        print(f"❌ 实时数据目录不存在: {realtime_dir}")
        return
    
    files = os.listdir(realtime_dir)
    if not files:
        print("⚠️ 实时数据目录为空")
        return
    
    print(f"📂 实时数据目录: {realtime_dir}")
    for file in files:
        filepath = os.path.join(realtime_dir, file)
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"✅ {file}: {len(data)} 个数据点")
            if data:
                print(f"   最新: {data[-1]['time']} - {data[-1]['data']}")
        except Exception as e:
            print(f"❌ {file}: 读取失败 - {e}")

if __name__ == '__main__':
    print("🚀 实时数据测试工具")
    print("=" * 50)
    
    # 生成测试数据
    generate_test_data()
    
    # 检查文件
    check_realtime_files()
    
    # 测试API（需要服务器运行）
    try:
        test_realtime_apis()
    except Exception as e:
        print(f"\n⚠️ API测试跳过（服务器可能未运行）: {e}")
    
    print("\n✨ 测试完成！")
    print("💡 提示：启动服务器后访问 http://localhost:5000/test_data.html 查看API响应")
