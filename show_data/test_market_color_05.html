<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大盘颜色测试05</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style.css">
    <style>
        body {
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .test-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-title {
            color: #1f2937;
            font-weight: 600;
            margin-bottom: 15px;
        }
        .status {
            padding: 8px 12px;
            border-radius: 6px;
            margin: 5px 0;
            font-weight: 500;
        }
        .status.success { background: #d1fae5; color: #065f46; }
        .status.error { background: #fee2e2; color: #991b1b; }
        .status.warning { background: #fef3c7; color: #92400e; }
        .status.info { background: #dbeafe; color: #1e40af; }
        .test-button {
            margin: 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
        }
        .test-button.up { background: #dc3545; color: white; }
        .test-button.down { background: #28a745; color: white; }
        .test-button.flat { background: #6c757d; color: white; }
        .test-button.normal { background: #007bff; color: white; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-card">
            <h3 class="test-title">大盘颜色动态变化测试05</h3>
            <p>测试大盘标题根据涨跌情况变化颜色：上涨红色、下跌绿色、横盘灰色</p>
            
            <div id="test-status" class="status info">🔄 开始测试...</div>
        </div>
        
        <!-- 大盘显示区域 -->
        <div class="test-card">
            <h5>大盘显示效果</h5>
            <div class="card summary-card-horizontal">
                <div class="card-body p-2">
                    <div class="d-flex align-items-center h-100">
                        <!-- 左侧：标题 -->
                        <div class="market-title">
                            <h6 class="mb-0"
                                style="font-size: 0.75rem; writing-mode: vertical-lr; text-orientation: mixed;">
                                <i class="fas fa-chart-bar"></i><br>大<br>盘
                            </h6>
                        </div>
                        <!-- 中间：横条区域 -->
                        <div class="market-bars-area flex-grow-1 mx-2">
                            <!-- 涨跌分布 -->
                            <div id="market-bars" class="market-bars-horizontal mb-1">
                                <div class="text-center">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status"
                                        style="width: 1rem; height: 1rem;">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                </div>
                            </div>
                            <!-- 涨跌停分布 -->
                            <div id="limit-bars" class="limit-bars-horizontal mb-1">
                                <div class="text-center">
                                    <div class="spinner-border spinner-border-sm text-warning" role="status"
                                        style="width: 1rem; height: 1rem;">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 右侧：成交金额 -->
                        <div class="volume-info-side">
                            <div id="volume-info" class="text-center">
                                <div class="text-muted">
                                    <small style="font-size: 0.7rem;">加载中...</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 测试控制区域 -->
        <div class="test-card">
            <h5>测试控制</h5>
            <p>点击按钮模拟不同的市场状态：</p>
            <button class="test-button up" onclick="testMarketState('up')">上涨市场 (涨3000跌1000)</button>
            <button class="test-button down" onclick="testMarketState('down')">下跌市场 (涨1000跌3000)</button>
            <button class="test-button flat" onclick="testMarketState('flat')">横盘市场 (涨2000跌2000)</button>
            <button class="test-button normal" onclick="loadRealData()">加载真实数据</button>
        </div>
        
        <!-- 状态说明 -->
        <div class="test-card">
            <h5>颜色状态说明</h5>
            <div class="row">
                <div class="col-md-4">
                    <div class="market-title market-up" style="width: 60px; height: 60px; margin: 10px auto;">
                        <h6 class="mb-0" style="font-size: 0.7rem; writing-mode: vertical-lr;">上涨</h6>
                    </div>
                    <p class="text-center"><strong>红色</strong>：上涨股票明显多于下跌股票</p>
                </div>
                <div class="col-md-4">
                    <div class="market-title market-down" style="width: 60px; height: 60px; margin: 10px auto;">
                        <h6 class="mb-0" style="font-size: 0.7rem; writing-mode: vertical-lr;">下跌</h6>
                    </div>
                    <p class="text-center"><strong>绿色</strong>：下跌股票明显多于上涨股票</p>
                </div>
                <div class="col-md-4">
                    <div class="market-title market-flat" style="width: 60px; height: 60px; margin: 10px auto;">
                        <h6 class="mb-0" style="font-size: 0.7rem; writing-mode: vertical-lr;">横盘</h6>
                    </div>
                    <p class="text-center"><strong>灰色</strong>：涨跌相当或平盘较多</p>
                </div>
            </div>
        </div>
        
        <!-- 控制台日志显示 -->
        <div class="test-card">
            <h5>控制台日志</h5>
            <div id="console-logs" style="background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace; font-size: 0.85rem; max-height: 200px; overflow-y: auto;">
                <div>等待日志输出...</div>
            </div>
        </div>
    </div>

    <!-- 引入必要的JavaScript文件 -->
    <script src="/static/js/charts.js"></script>
    
    <script>
        // 捕获控制台日志
        const originalLog = console.log;
        const logsContainer = document.getElementById('console-logs');
        
        function addLogToDisplay(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.createElement('div');
            logDiv.style.marginBottom = '5px';
            logDiv.innerHTML = `[${timestamp}] ${message}`;
            logsContainer.appendChild(logDiv);
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLogToDisplay(args.join(' '));
        };
        
        // 测试不同市场状态
        function testMarketState(state) {
            let riseCount, fallCount, flatCount;
            
            switch(state) {
                case 'up':
                    riseCount = 3000;
                    fallCount = 1000;
                    flatCount = 1000;
                    break;
                case 'down':
                    riseCount = 1000;
                    fallCount = 3000;
                    flatCount = 1000;
                    break;
                case 'flat':
                    riseCount = 2000;
                    fallCount = 2000;
                    flatCount = 1000;
                    break;
            }
            
            // 模拟市场数据
            const mockData = [{
                rise_count: riseCount,
                fall_count: fallCount,
                flat_count: flatCount,
                volume: 22900,
                volume_formatted: '2.29万亿'
            }];
            
            console.log(`🧪 测试${state === 'up' ? '上涨' : state === 'down' ? '下跌' : '横盘'}市场状态`);
            
            // 调用更新函数
            if (typeof updateMarketBars === 'function') {
                updateMarketBars(mockData);
            } else {
                console.error('updateMarketBars函数未定义');
            }
        }
        
        // 加载真实数据
        async function loadRealData() {
            try {
                console.log('📡 加载真实市场数据...');
                const response = await fetch('/api/market_summary');
                const result = await response.json();
                
                if (result.success && result.data.length > 0) {
                    console.log('✅ 真实数据加载成功');
                    updateMarketBars(result.data);
                } else {
                    console.error('❌ 真实数据加载失败');
                }
            } catch (error) {
                console.error('❌ 加载真实数据异常:', error);
            }
        }
        
        // 页面加载完成后开始测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 页面加载完成，开始大盘颜色测试...');
            
            const testStatus = document.getElementById('test-status');
            testStatus.className = 'status success';
            testStatus.textContent = '✅ 测试环境准备完成，请点击按钮测试不同市场状态';
            
            // 默认加载真实数据
            setTimeout(loadRealData, 1000);
        });
    </script>
</body>
</html>
