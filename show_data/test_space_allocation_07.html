<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>连板金字塔空间分配优化测试07</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style.css">
    <style>
        body {
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-title {
            color: #1f2937;
            font-weight: 600;
            margin-bottom: 15px;
        }
        .status {
            padding: 8px 12px;
            border-radius: 6px;
            margin: 5px 0;
            font-weight: 500;
        }
        .status.success { background: #d1fae5; color: #065f46; }
        .status.error { background: #fee2e2; color: #991b1b; }
        .status.warning { background: #fef3c7; color: #92400e; }
        .status.info { background: #dbeafe; color: #1e40af; }
        .allocation-card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f9fafb;
        }
        .allocation-bar {
            height: 20px;
            border-radius: 4px;
            margin: 5px 0;
            position: relative;
            overflow: hidden;
        }
        .allocation-bar.high-level { background: #fef2f2; border: 1px solid #dc2626; }
        .allocation-bar.mid-level { background: #fffbeb; border: 1px solid #d97706; }
        .allocation-bar.low-level { background: #ecfdf5; border: 1px solid #059669; }
        .allocation-fill {
            height: 100%;
            transition: width 0.3s ease;
        }
        .allocation-fill.high-level { background: linear-gradient(90deg, #dc2626, #ef4444); }
        .allocation-fill.mid-level { background: linear-gradient(90deg, #d97706, #f59e0b); }
        .allocation-fill.low-level { background: linear-gradient(90deg, #059669, #10b981); }
        .allocation-text {
            position: absolute;
            left: 8px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.75rem;
            font-weight: 500;
            color: #374151;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-card">
            <h3 class="test-title">连板金字塔空间分配优化测试07</h3>
            <p>测试基于股票数量的智能空间分配策略和图标对齐优化</p>
            
            <div id="test-status" class="status info">🔄 开始测试...</div>
        </div>
        
        <!-- 空间分配策略说明 -->
        <div class="test-card">
            <h5>优化策略说明</h5>
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">📊 基于股票数量的空间分配</h6>
                    <ul class="small">
                        <li>根据各级别股票数量占比分配基础空间</li>
                        <li>股票多的级别获得更多显示空间</li>
                        <li>确保能显示更多股票信息</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="text-success">🎯 级别权重调整</h6>
                    <ul class="small">
                        <li>超高级别(5+): 权重1.2</li>
                        <li>高级别(3-4): 权重1.1</li>
                        <li>中级别(2): 权重1.0</li>
                        <li>低级别(0-1): 权重0.9</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 连板金字塔显示区域 -->
        <div class="test-card">
            <h5>连板金字塔显示效果</h5>
            <div class="pyramid-card-new" style="height: 600px;">
                <div class="card-header py-2 text-center border-0 bg-gradient-to-r from-blue-50 to-purple-50">
                    <h6 class="mb-0 font-semibold text-gray-800">连板金字塔</h6>
                    <small class="text-gray-500" id="pyramid-date">加载中...</small>
                </div>
                <div class="card-body p-3">
                    <div class="pyramid-layout-fullwidth">
                        <div id="lianban-pyramid-visual" class="pyramid-container-fullwidth">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2 text-muted">正在加载连板数据...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 空间分配分析 -->
        <div class="test-card">
            <h5>空间分配分析</h5>
            <div id="allocation-analysis">
                <div class="text-muted">等待数据加载完成...</div>
            </div>
        </div>
        
        <!-- 优化效果对比 -->
        <div class="test-card">
            <h5>优化效果</h5>
            <div class="row">
                <div class="col-md-4">
                    <div class="text-center p-3 border rounded">
                        <h6 class="text-success">整体胜率区域</h6>
                        <p class="small text-muted">减少padding和margin<br>优化字体大小<br>腾出更多空间</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center p-3 border rounded">
                        <h6 class="text-primary">图标对齐</h6>
                        <p class="small text-muted">统一高度设置<br>垂直居中对齐<br>视觉效果更佳</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center p-3 border rounded">
                        <h6 class="text-warning">空间利用</h6>
                        <p class="small text-muted">基于股票数量<br>智能分配空间<br>显示更多信息</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 控制台日志显示 -->
        <div class="test-card">
            <h5>控制台日志</h5>
            <div id="console-logs" style="background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace; font-size: 0.85rem; max-height: 300px; overflow-y: auto;">
                <div>等待日志输出...</div>
            </div>
        </div>
    </div>

    <!-- 引入必要的JavaScript文件 -->
    <script src="/static/js/charts.js"></script>
    
    <script>
        // 捕获控制台日志
        const originalLog = console.log;
        const logsContainer = document.getElementById('console-logs');
        
        function addLogToDisplay(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.createElement('div');
            logDiv.style.marginBottom = '5px';
            logDiv.innerHTML = `[${timestamp}] ${message}`;
            logsContainer.appendChild(logDiv);
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLogToDisplay(args.join(' '));
        };
        
        // 分析空间分配效果
        function analyzeSpaceAllocation(data, stocksData) {
            const analysisContainer = document.getElementById('allocation-analysis');
            
            if (!stocksData) {
                analysisContainer.innerHTML = '<div class="alert alert-warning">暂无股票数据进行分析</div>';
                return;
            }
            
            // 计算总股票数
            const levels = Object.keys(stocksData);
            const totalStocks = levels.reduce((sum, level) => sum + stocksData[level].length, 0);
            
            let analysisHtml = '<div class="mb-3"><h6>空间分配详情</h6></div>';
            
            // 按优先级排序
            const sortedLevels = levels.sort((a, b) => {
                const getPriority = (level) => {
                    if (level === '首板') return 0;
                    if (level === '1进2') return 1;
                    if (level === '2进3') return 2;
                    const match = level.match(/(\d+)进(\d+)/);
                    return match ? parseInt(match[1]) : 0;
                };
                return getPriority(b) - getPriority(a);
            });
            
            sortedLevels.forEach(level => {
                const stocks = stocksData[level];
                const stockCount = stocks.length;
                const stockRatio = (stockCount / totalStocks * 100).toFixed(1);
                
                const priority = getLevelPriority ? getLevelPriority(level) : 0;
                let categoryClass = 'low-level';
                let categoryName = '低级别';
                let weight = 0.9;
                
                if (priority >= 5) {
                    categoryClass = 'high-level';
                    categoryName = '超高级别';
                    weight = 1.2;
                } else if (priority >= 3) {
                    categoryClass = 'high-level';
                    categoryName = '高级别';
                    weight = 1.1;
                } else if (priority === 2) {
                    categoryClass = 'mid-level';
                    categoryName = '中级别';
                    weight = 1.0;
                }
                
                const adjustedRatio = (stockRatio * weight).toFixed(1);
                
                analysisHtml += `
                <div class="allocation-card">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-bold">${level} (${categoryName})</span>
                        <span class="badge bg-secondary">${stockCount}只股票</span>
                    </div>
                    <div class="allocation-bar ${categoryClass}">
                        <div class="allocation-fill ${categoryClass}" style="width: ${adjustedRatio}%"></div>
                        <div class="allocation-text">
                            基础占比: ${stockRatio}% | 权重: ${weight} | 调整后: ${adjustedRatio}%
                        </div>
                    </div>
                </div>
                `;
            });
            
            analysisContainer.innerHTML = analysisHtml;
        }
        
        // 页面加载完成后开始测试
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 页面加载完成，开始空间分配优化测试...');
            
            const testStatus = document.getElementById('test-status');
            
            try {
                testStatus.className = 'status info';
                testStatus.textContent = '📡 正在加载连板数据...';
                
                // 加载连板数据
                const response = await fetch('/api/lianban_progress');
                const result = await response.json();
                
                if (result.success) {
                    console.log('✅ 连板数据加载成功');
                    testStatus.className = 'status success';
                    testStatus.textContent = '✅ 数据加载成功，正在应用空间分配优化...';
                    
                    // 更新金字塔显示
                    if (typeof updateModernPyramid === 'function') {
                        updateModernPyramid(result.data, result.latest_stocks);
                        
                        // 分析空间分配
                        setTimeout(() => {
                            analyzeSpaceAllocation(result.data, result.latest_stocks);
                        }, 1000);
                        
                        testStatus.className = 'status success';
                        testStatus.textContent = '🎉 空间分配优化测试完成！';
                    } else {
                        throw new Error('updateModernPyramid函数未定义');
                    }
                } else {
                    throw new Error(result.error || '数据加载失败');
                }
            } catch (error) {
                console.error('❌ 测试失败:', error);
                testStatus.className = 'status error';
                testStatus.textContent = `❌ 测试失败: ${error.message}`;
            }
        });
    </script>
</body>
</html>
