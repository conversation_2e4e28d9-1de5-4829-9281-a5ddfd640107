<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>连板金字塔测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style.css">
    <style>
        body {
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
        }
        .test-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-title {
            color: #1f2937;
            font-weight: 600;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-card">
            <h3 class="test-title">连板金字塔功能测试</h3>
            <p class="text-muted">测试修复后的股票显示和胜率统计功能</p>
            
            <!-- 连板金字塔测试区域 -->
            <div class="pyramid-card-new" style="height: 500px;">
                <div class="card-header py-2 text-center border-0 bg-gradient-to-r from-blue-50 to-purple-50">
                    <h6 class="mb-0 font-semibold text-gray-800">连板金字塔</h6>
                    <small class="text-gray-500" id="pyramid-date">加载中...</small>
                </div>
                <div class="card-body p-3">
                    <div class="pyramid-layout-fullwidth">
                        <div id="lianban-pyramid-visual" class="pyramid-container-fullwidth">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2 text-muted">正在加载连板数据...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 测试按钮 -->
            <div class="mt-3">
                <button class="btn btn-primary" onclick="loadTestData()">加载测试数据</button>
                <button class="btn btn-secondary" onclick="loadRealData()">加载真实数据</button>
                <button class="btn btn-info" onclick="testLargeData()">测试大量股票</button>
            </div>
        </div>
        
        <!-- 测试结果显示 -->
        <div class="test-card">
            <h5 class="test-title">测试结果</h5>
            <div id="test-results">
                <p class="text-muted">点击上方按钮开始测试...</p>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/utils.js"></script>
    <script src="/static/js/charts.js"></script>
    
    <script>
        // 测试数据
        function loadTestData() {
            const testProgressData = [{
                date: '2025-08-14',
                progress: {
                    '首板': { success: 29, total: 56, percentage: 52 },
                    '2进3': { success: 4, total: 12, percentage: 33 },
                    '3进4': { success: 1, total: 9, percentage: 11 },
                    '4进5': { success: 0, total: 2, percentage: 0 },
                    '5进6': { success: 0, total: 1, percentage: 0 }
                }
            }];
            
            const testStocksData = {
                '首板': [
                    { name: '有方科技', code: '688159', status: '成', change_percent: '20.01', industry: '国防军工' },
                    { name: '博拓生物', code: '688767', status: '成', change_percent: '19.99', industry: '国防军工' },
                    { name: '通鼎互联', code: '002491', status: '成', change_percent: '10.06', industry: '国防军工' },
                    { name: '升达林业', code: '002259', status: '成', change_percent: '10.05', industry: '国防军工' },
                    { name: '山东章鼓', code: '002598', status: '成', change_percent: '10.03', industry: '国防军工' }
                ],
                '2进3': [
                    { name: '生益科技', code: '600183', status: '成', change_percent: '10.02', industry: '电子' },
                    { name: '创新医疗', code: '002173', status: '成', change_percent: '10.02', industry: '医疗' },
                    { name: '徐家汇', code: '002561', status: '炸', change_percent: '10.00', industry: '商业' },
                    { name: '电子城', code: '600658', status: '', change_percent: '-9.98', industry: '房地产' }
                ],
                '3进4': [
                    { name: '大东南', code: '002263', status: '成', change_percent: '10.01', industry: '造纸' }
                ],
                '4进5': [
                    { name: '万盛股份', code: '603010', status: '炸', change_percent: '7.88', industry: '化工' },
                    { name: '新奥股份', code: '603393', status: '', change_percent: '-10.01', industry: '化工' }
                ],
                '5进6': [
                    { name: '复旦微电', code: '688385', status: '', change_percent: '1.97', industry: '半导体' }
                ]
            };
            
            updateModernPyramid(testProgressData, testStocksData);
            updateTestResults('测试数据加载成功！显示了5个级别的连板数据，包含不同状态的股票。');
        }
        
        // 加载真实数据
        function loadRealData() {
            fetch('/api/lianban_progress')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateModernPyramid(data.data, data.latest_stocks);
                        updateTestResults('真实数据加载成功！从API获取了最新的连板数据。');
                    } else {
                        updateTestResults('真实数据加载失败：' + data.error);
                    }
                })
                .catch(error => {
                    updateTestResults('真实数据加载出错：' + error.message);
                });
        }
        
        // 测试大量股票数据
        function testLargeData() {
            const largeStocksData = {
                '首板': []
            };
            
            // 生成50只首板股票
            for (let i = 1; i <= 50; i++) {
                largeStocksData['首板'].push({
                    name: `测试股票${i}`,
                    code: `00${String(i).padStart(4, '0')}`,
                    status: i <= 25 ? '成' : (i <= 40 ? '炸' : ''),
                    change_percent: (Math.random() * 20 - 10).toFixed(2),
                    industry: '测试行业'
                });
            }
            
            const largeProgressData = [{
                date: '2025-08-14',
                progress: {
                    '首板': { success: 25, total: 50, percentage: 50 }
                }
            }];
            
            updateModernPyramid(largeProgressData, largeStocksData);
            updateTestResults('大量股票测试完成！显示了50只首板股票，测试智能布局算法。');
        }
        
        function updateTestResults(message) {
            document.getElementById('test-results').innerHTML = `
                <div class="alert alert-info">
                    <strong>测试结果：</strong> ${message}
                    <br><small>时间：${new Date().toLocaleString()}</small>
                </div>
            `;
        }
        
        // 页面加载时自动加载测试数据
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(loadTestData, 1000);
        });
    </script>
</body>
</html>
