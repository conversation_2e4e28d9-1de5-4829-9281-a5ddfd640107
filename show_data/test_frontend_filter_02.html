<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试程序02：前端时间过滤功能验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: #f8f9fa;
        }
        .auction-time {
            background-color: #fff3cd;
            color: #856404;
        }
        .market-time {
            background-color: #d4edda;
            color: #155724;
        }
        .other-time {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 测试程序02：前端时间过滤功能验证</h1>
        <p>测试JavaScript中的时间过滤逻辑是否正确过滤集合竞价时间段(9:15-9:30)的数据</p>
        
        <div class="test-section">
            <div class="test-title">📊 JavaScript时间判断函数测试</div>
            <div id="js-function-test"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">🌐 API数据过滤测试</div>
            <div id="api-filter-test"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">📋 详细数据分析</div>
            <div id="detailed-analysis"></div>
        </div>
    </div>

    <script>
        // 复制charts.js中的时间判断函数
        function isMarketOpen(timeStr) {
            if (!timeStr) return false;

            // 解析时间字符串 (HH:MM 格式)
            const timeParts = timeStr.split(':');
            if (timeParts.length !== 2) return false;

            const hour = parseInt(timeParts[0]);
            const minute = parseInt(timeParts[1]);
            const totalMinutes = hour * 60 + minute;

            // 开盘时间段：9:30-11:30 (570-690分钟), 13:00-15:00 (780-900分钟)
            const morningStart = 9 * 60 + 30;  // 570
            const morningEnd = 11 * 60 + 30;   // 690
            const afternoonStart = 13 * 60;    // 780
            const afternoonEnd = 15 * 60;      // 900

            return (totalMinutes >= morningStart && totalMinutes <= morningEnd) ||
                   (totalMinutes >= afternoonStart && totalMinutes <= afternoonEnd);
        }

        // 复制charts.js中的数据过滤函数
        function filterMarketHoursData(data) {
            if (!Array.isArray(data)) return [];

            return data.filter(item => {
                if (!item.time) return false;
                return isMarketOpen(item.time);
            });
        }

        // 判断是否为集合竞价时间
        function isAuctionTime(timeStr) {
            if (!timeStr) return false;
            
            const timeParts = timeStr.split(':');
            if (timeParts.length !== 2) return false;
            
            const hour = parseInt(timeParts[0]);
            const minute = parseInt(timeParts[1]);
            const totalMinutes = hour * 60 + minute;
            
            // 集合竞价时间段：9:15-9:30 (555-570分钟)
            const auctionStart = 9 * 60 + 15;  // 555
            const auctionEnd = 9 * 60 + 30;    // 570
            
            return totalMinutes >= auctionStart && totalMinutes < auctionEnd;
        }

        // 测试JavaScript函数
        function testJavaScriptFunctions() {
            const testTimes = [
                '09:10', '09:15', '09:20', '09:25', '09:30', '09:35',
                '10:00', '11:30', '12:00', '13:00', '14:00', '15:00', '15:30'
            ];
            
            let results = [];
            let auctionFiltered = 0;
            let marketKept = 0;
            
            testTimes.forEach(time => {
                const isMarket = isMarketOpen(time);
                const isAuction = isAuctionTime(time);
                
                let status = '其他时间';
                let className = 'other-time';
                
                if (isAuction) {
                    status = '集合竞价';
                    className = 'auction-time';
                    if (!isMarket) auctionFiltered++;
                } else if (isMarket) {
                    status = '开盘时间';
                    className = 'market-time';
                    marketKept++;
                }
                
                results.push({
                    time: time,
                    isMarket: isMarket,
                    isAuction: isAuction,
                    status: status,
                    className: className
                });
            });
            
            // 生成测试结果HTML
            let html = '<table class="data-table">';
            html += '<tr><th>时间</th><th>isMarketOpen()</th><th>isAuctionTime()</th><th>状态</th><th>过滤结果</th></tr>';
            
            results.forEach(result => {
                const filterResult = result.isMarket ? '保留' : '过滤';
                html += `<tr class="${result.className}">`;
                html += `<td>${result.time}</td>`;
                html += `<td>${result.isMarket}</td>`;
                html += `<td>${result.isAuction}</td>`;
                html += `<td>${result.status}</td>`;
                html += `<td>${filterResult}</td>`;
                html += '</tr>';
            });
            html += '</table>';
            
            // 添加总结
            const auctionTotal = results.filter(r => r.isAuction).length;
            const marketTotal = results.filter(r => r.isMarket).length;
            
            html += `<div class="test-result ${auctionFiltered === auctionTotal && marketKept === marketTotal ? 'success' : 'error'}">`;
            html += `<strong>JavaScript函数测试结果：</strong><br>`;
            html += `集合竞价时间段: ${auctionTotal}个，过滤: ${auctionFiltered}个<br>`;
            html += `开盘时间段: ${marketTotal}个，保留: ${marketKept}个<br>`;
            html += `过滤效果: ${auctionFiltered === auctionTotal && marketKept === marketTotal ? '✅ 正确' : '❌ 异常'}`;
            html += '</div>';
            
            document.getElementById('js-function-test').innerHTML = html;
        }

        // 测试API数据过滤
        async function testAPIDataFilter() {
            try {
                const response = await fetch('/api/realtime/history/market');
                const result = await response.json();
                
                if (result.success) {
                    const apiData = result.data;
                    
                    // 分析API数据
                    let auctionCount = 0;
                    let marketCount = 0;
                    let otherCount = 0;
                    
                    apiData.forEach(item => {
                        const time = item.time;
                        if (isAuctionTime(time)) {
                            auctionCount++;
                        } else if (isMarketOpen(time)) {
                            marketCount++;
                        } else {
                            otherCount++;
                        }
                    });
                    
                    let html = `<div class="test-result info">`;
                    html += `<strong>API数据分析：</strong><br>`;
                    html += `总数据点: ${apiData.length}个<br>`;
                    html += `集合竞价时间段: ${auctionCount}个<br>`;
                    html += `开盘时间段: ${marketCount}个<br>`;
                    html += `其他时间段: ${otherCount}个`;
                    html += '</div>';
                    
                    html += `<div class="test-result ${auctionCount === 0 && marketCount > 0 ? 'success' : 'error'}">`;
                    html += `<strong>API过滤效果：</strong><br>`;
                    html += `${auctionCount === 0 ? '✅ 集合竞价数据已被正确过滤' : '❌ 集合竞价数据未被过滤'}<br>`;
                    html += `${marketCount > 0 ? '✅ 开盘时间数据正常显示' : '❌ 开盘时间数据被错误过滤'}`;
                    html += '</div>';
                    
                    document.getElementById('api-filter-test').innerHTML = html;
                    
                    // 显示详细数据分析
                    showDetailedAnalysis(apiData);
                } else {
                    document.getElementById('api-filter-test').innerHTML = 
                        `<div class="test-result error">API请求失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('api-filter-test').innerHTML = 
                    `<div class="test-result error">API测试异常: ${error.message}</div>`;
            }
        }

        // 显示详细数据分析
        function showDetailedAnalysis(data) {
            let html = '<table class="data-table">';
            html += '<tr><th>序号</th><th>时间</th><th>状态</th><th>上涨数</th><th>下跌数</th><th>涨停数</th><th>跌停数</th></tr>';
            
            data.slice(0, 10).forEach((item, index) => {
                const time = item.time;
                let status = '其他时间';
                let className = 'other-time';
                
                if (isAuctionTime(time)) {
                    status = '集合竞价';
                    className = 'auction-time';
                } else if (isMarketOpen(time)) {
                    status = '开盘时间';
                    className = 'market-time';
                }
                
                html += `<tr class="${className}">`;
                html += `<td>${index + 1}</td>`;
                html += `<td>${time}</td>`;
                html += `<td>${status}</td>`;
                html += `<td>${item.data.up_count || 0}</td>`;
                html += `<td>${item.data.down_count || 0}</td>`;
                html += `<td>${item.data.limit_up_count || 0}</td>`;
                html += `<td>${item.data.limit_down_count || 0}</td>`;
                html += '</tr>';
            });
            
            if (data.length > 10) {
                html += `<tr><td colspan="7" style="text-align: center; font-style: italic;">... 还有 ${data.length - 10} 个数据点</td></tr>`;
            }
            
            html += '</table>';
            
            document.getElementById('detailed-analysis').innerHTML = html;
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 开始前端时间过滤功能测试');
            testJavaScriptFunctions();
            testAPIDataFilter();
        });
    </script>
</body>
</html>
