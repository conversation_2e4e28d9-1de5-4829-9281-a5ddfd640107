<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实API数据修复测试 09</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="static/css/style.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
        
        .pyramid-test-container {
            height: 700px;
            border: 2px solid #007bff;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .metric-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 12px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: #495057;
        }
        
        .metric-label {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 4px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">🔧 真实API数据修复测试 09</h1>
        
        <div class="alert alert-info">
            <h5>📊 测试目标</h5>
            <p>使用真实API数据验证紧急修复的效果，确保首板股票正常显示且标签完美对齐。</p>
        </div>

        <!-- API状态检查 -->
        <div class="test-card">
            <h5>🌐 API连接状态</h5>
            <div class="row">
                <div class="col-md-6">
                    <div><span id="api-status" class="status-indicator status-warning"></span>API连接状态: <span id="api-status-text">检查中...</span></div>
                    <div><span id="data-status" class="status-indicator status-warning"></span>数据加载状态: <span id="data-status-text">等待中...</span></div>
                </div>
                <div class="col-md-6">
                    <div><span id="render-status" class="status-indicator status-warning"></span>渲染状态: <span id="render-status-text">等待中...</span></div>
                    <div><span id="fix-status" class="status-indicator status-warning"></span>修复效果: <span id="fix-status-text">验证中...</span></div>
                </div>
            </div>
        </div>

        <!-- 数据统计 -->
        <div class="test-card">
            <h5>📈 数据统计</h5>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="total-levels">-</div>
                    <div class="metric-label">连板级别数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="total-stocks">-</div>
                    <div class="metric-label">总股票数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="shouban-stocks">-</div>
                    <div class="metric-label">首板股票数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="displayed-stocks">-</div>
                    <div class="metric-label">显示股票数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="shouban-displayed">-</div>
                    <div class="metric-label">首板显示数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="alignment-score">-</div>
                    <div class="metric-label">对齐评分</div>
                </div>
            </div>
        </div>

        <!-- 金字塔显示区域 -->
        <div class="test-card">
            <h5>🏗️ 连板金字塔显示</h5>
            <div class="pyramid-test-container">
                <div class="pyramid-card-new" style="height: 100%;">
                    <div class="card-header py-2 text-center border-0 bg-gradient-to-r from-blue-50 to-purple-50">
                        <h6 class="mb-0 font-semibold text-gray-800">连板金字塔 - 真实数据测试</h6>
                        <small class="text-gray-500" id="pyramid-date">加载中...</small>
                    </div>
                    <div class="card-body p-3">
                        <div class="pyramid-layout-fullwidth">
                            <div id="lianban-pyramid-visual" class="pyramid-container-fullwidth">
                                <div class="text-center">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2 text-muted">正在从API加载真实数据...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 修复验证详情 -->
        <div class="test-card">
            <h5>✅ 修复验证详情</h5>
            <div id="verification-details">
                <div class="text-muted">等待数据加载完成...</div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="test-card">
            <h5>🎮 测试操作</h5>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-primary" onclick="reloadData()">重新加载数据</button>
                <button type="button" class="btn btn-outline-secondary" onclick="checkAlignment()">检查对齐</button>
                <button type="button" class="btn btn-outline-info" onclick="analyzeLayout()">分析布局</button>
                <button type="button" class="btn btn-outline-success" onclick="exportReport()">导出报告</button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="static/js/charts.js"></script>
    <script>
        let currentData = null;
        let currentStocks = null;

        // 更新状态指示器
        function updateStatus(elementId, status, text) {
            const indicator = document.getElementById(elementId);
            const textElement = document.getElementById(elementId + '-text');
            
            indicator.className = `status-indicator status-${status}`;
            textElement.textContent = text;
        }

        // 更新指标
        function updateMetric(metricId, value) {
            document.getElementById(metricId).textContent = value;
        }

        // 加载真实API数据
        async function loadRealData() {
            try {
                updateStatus('api-status', 'warning', '连接中...');
                
                const response = await fetch('/api/lianban_progress');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                updateStatus('api-status', 'success', '连接成功');
                updateStatus('data-status', 'warning', '解析中...');
                
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.error || '数据获取失败');
                }
                
                currentData = data.data;
                currentStocks = data.latest_stocks;
                
                updateStatus('data-status', 'success', '数据加载成功');
                updateStatus('render-status', 'warning', '渲染中...');
                
                // 计算统计信息
                calculateStatistics();
                
                // 渲染金字塔
                updateModernPyramid(currentData, currentStocks);
                
                updateStatus('render-status', 'success', '渲染完成');
                
                // 延迟验证修复效果
                setTimeout(() => {
                    verifyFixEffects();
                }, 1000);
                
            } catch (error) {
                console.error('加载数据失败:', error);
                updateStatus('api-status', 'error', '连接失败');
                updateStatus('data-status', 'error', error.message);
                updateStatus('render-status', 'error', '渲染失败');
                updateStatus('fix-status', 'error', '验证失败');
            }
        }

        // 计算统计信息
        function calculateStatistics() {
            if (!currentData || !currentStocks) return;
            
            const levels = Object.keys(currentStocks);
            const totalStocks = Object.values(currentStocks).reduce((sum, stocks) => sum + stocks.length, 0);
            const shoubanStocks = currentStocks['首板'] ? currentStocks['首板'].length : 0;
            
            updateMetric('total-levels', levels.length);
            updateMetric('total-stocks', totalStocks);
            updateMetric('shouban-stocks', shoubanStocks);
        }

        // 验证修复效果
        function verifyFixEffects() {
            const results = [];
            
            try {
                // 检查首板股票显示
                const pyramidContent = document.getElementById('lianban-pyramid-visual').innerHTML;
                const shoubanVisible = pyramidContent.includes('首板') && pyramidContent.includes('stock-item');
                
                if (shoubanVisible) {
                    results.push('✅ 首板股票正常显示');
                    updateStatus('fix-status', 'success', '修复成功');
                } else {
                    results.push('❌ 首板股票显示异常');
                    updateStatus('fix-status', 'error', '修复失败');
                }
                
                // 统计显示的股票数量
                const stockItems = document.querySelectorAll('.stock-item:not(.more-stocks)');
                const shoubanItems = document.querySelectorAll('.lianban-level-row:first-child .stock-item:not(.more-stocks)');
                
                updateMetric('displayed-stocks', stockItems.length);
                updateMetric('shouban-displayed', shoubanItems.length);
                
                // 检查标签对齐
                const levelInfos = document.querySelectorAll('.level-info');
                let alignmentScore = 0;
                
                levelInfos.forEach(info => {
                    const badge = info.querySelector('.level-badge');
                    const stats = info.querySelector('.level-stats');
                    
                    if (badge && stats) {
                        const badgeRect = badge.getBoundingClientRect();
                        const statsRect = stats.getBoundingClientRect();
                        
                        // 检查垂直对齐
                        const verticalDiff = Math.abs(badgeRect.top - statsRect.top);
                        if (verticalDiff < 5) alignmentScore += 20;
                    }
                });
                
                updateMetric('alignment-score', `${alignmentScore}%`);
                
                if (alignmentScore >= 80) {
                    results.push('✅ 标签对齐良好');
                } else {
                    results.push('⚠️ 标签对齐需要优化');
                }
                
                // 检查高度分配
                const levelRows = document.querySelectorAll('.lianban-level-row');
                let heightReasonable = true;
                
                levelRows.forEach(row => {
                    const height = row.offsetHeight;
                    if (height < 50) {
                        heightReasonable = false;
                    }
                });
                
                if (heightReasonable) {
                    results.push('✅ 高度分配合理');
                } else {
                    results.push('⚠️ 高度分配需要调整');
                }
                
                // 更新验证详情
                document.getElementById('verification-details').innerHTML = 
                    '<div class="small">' + results.join('<br>') + '</div>';
                
            } catch (error) {
                console.error('验证修复效果失败:', error);
                updateStatus('fix-status', 'error', '验证异常');
                document.getElementById('verification-details').innerHTML = 
                    '<div class="text-danger">验证过程出现异常: ' + error.message + '</div>';
            }
        }

        // 重新加载数据
        function reloadData() {
            // 重置状态
            updateStatus('api-status', 'warning', '重新连接...');
            updateStatus('data-status', 'warning', '等待中...');
            updateStatus('render-status', 'warning', '等待中...');
            updateStatus('fix-status', 'warning', '验证中...');
            
            // 清空指标
            ['total-levels', 'total-stocks', 'shouban-stocks', 'displayed-stocks', 'shouban-displayed', 'alignment-score'].forEach(id => {
                updateMetric(id, '-');
            });
            
            // 重新加载
            loadRealData();
        }

        // 检查对齐
        function checkAlignment() {
            const levelInfos = document.querySelectorAll('.level-info');
            const results = [];
            
            levelInfos.forEach((info, index) => {
                const badge = info.querySelector('.level-badge');
                const stats = info.querySelector('.level-stats');
                
                if (badge && stats) {
                    const badgeRect = badge.getBoundingClientRect();
                    const statsRect = stats.getBoundingClientRect();
                    
                    const verticalDiff = Math.abs(badgeRect.top - statsRect.top);
                    const horizontalGap = statsRect.left - badgeRect.right;
                    
                    results.push(`级别${index + 1}: 垂直偏差${verticalDiff.toFixed(1)}px, 水平间距${horizontalGap.toFixed(1)}px`);
                }
            });
            
            alert('对齐检查结果:\n' + results.join('\n'));
        }

        // 分析布局
        function analyzeLayout() {
            const levelRows = document.querySelectorAll('.lianban-level-row');
            const analysis = [];
            
            levelRows.forEach((row, index) => {
                const levelBadge = row.querySelector('.level-badge');
                const stockItems = row.querySelectorAll('.stock-item:not(.more-stocks)');
                const moreButton = row.querySelector('.more-stocks');
                
                const level = levelBadge ? levelBadge.textContent : `级别${index + 1}`;
                const height = row.offsetHeight;
                const stockCount = stockItems.length;
                const hasMore = !!moreButton;
                
                analysis.push(`${level}: 高度${height}px, 显示${stockCount}只股票${hasMore ? ' (+更多)' : ''}`);
            });
            
            alert('布局分析:\n' + analysis.join('\n'));
        }

        // 导出报告
        function exportReport() {
            const report = {
                timestamp: new Date().toISOString(),
                test_type: 'real_api_fix_09',
                metrics: {
                    total_levels: document.getElementById('total-levels').textContent,
                    total_stocks: document.getElementById('total-stocks').textContent,
                    shouban_stocks: document.getElementById('shouban-stocks').textContent,
                    displayed_stocks: document.getElementById('displayed-stocks').textContent,
                    shouban_displayed: document.getElementById('shouban-displayed').textContent,
                    alignment_score: document.getElementById('alignment-score').textContent
                },
                status: {
                    api: document.getElementById('api-status-text').textContent,
                    data: document.getElementById('data-status-text').textContent,
                    render: document.getElementById('render-status-text').textContent,
                    fix: document.getElementById('fix-status-text').textContent
                },
                verification: document.getElementById('verification-details').textContent
            };
            
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `real_api_fix_09_report_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // 页面加载完成后自动开始测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('真实API数据修复测试页面初始化完成');
            loadRealData();
        });
    </script>
</body>
</html>
