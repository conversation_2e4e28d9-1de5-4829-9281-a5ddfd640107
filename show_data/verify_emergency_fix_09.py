#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连板金字塔紧急修复验证脚本 09
验证首板股票显示和标签对齐问题的修复效果
"""

import os
import re
import json
from datetime import datetime

def check_javascript_fixes():
    """检查JavaScript修复代码"""
    print("🔧 检查JavaScript紧急修复...")
    
    try:
        with open("static/js/charts.js", "r", encoding="utf-8") as f:
            content = f.read()
        
        fixes_checked = []
        
        # 检查首板特殊处理
        if "level === '首板' || level === '1进2'" in content:
            fixes_checked.append("✅ 首板特殊处理逻辑已添加")
        else:
            fixes_checked.append("❌ 首板特殊处理逻辑缺失")
        
        # 检查安全容器高度
        if "safeContainerHeight = Math.max(containerHeight, minContainerHeight)" in content:
            fixes_checked.append("✅ 安全容器高度检查已添加")
        else:
            fixes_checked.append("❌ 安全容器高度检查缺失")
        
        # 检查最小显示股票数量保证
        if "Math.min(stockCount, 20)" in content and "Math.min(stockCount, 30)" in content:
            fixes_checked.append("✅ 最小显示股票数量保证已添加")
        else:
            fixes_checked.append("❌ 最小显示股票数量保证缺失")
        
        # 检查高度分配特殊处理
        if "specialHeight" in content:
            fixes_checked.append("✅ 特殊高度分配逻辑已添加")
        else:
            fixes_checked.append("❌ 特殊高度分配逻辑缺失")
        
        # 检查最终安全检查
        if "maxDisplayStocks < 4 && stockCount > 0" in content:
            fixes_checked.append("✅ 最终安全检查已添加")
        else:
            fixes_checked.append("❌ 最终安全检查缺失")
        
        for check in fixes_checked:
            print(f"   {check}")
        
        return len([c for c in fixes_checked if c.startswith("✅")]) >= 4
        
    except Exception as e:
        print(f"❌ 检查JavaScript文件失败: {e}")
        return False

def check_css_fixes():
    """检查CSS修复代码"""
    print("\n🎨 检查CSS对齐修复...")
    
    try:
        with open("static/css/style.css", "r", encoding="utf-8") as f:
            content = f.read()
        
        fixes_checked = []
        
        # 检查级别信息容器宽度
        if "min-width: 120px" in content:
            fixes_checked.append("✅ 级别信息容器最小宽度已设置")
        else:
            fixes_checked.append("❌ 级别信息容器最小宽度未设置")
        
        # 检查justify-content设置
        if "justify-content: space-between" in content:
            fixes_checked.append("✅ 内部元素分布设置已添加")
        else:
            fixes_checked.append("❌ 内部元素分布设置缺失")
        
        # 检查级别徽章固定尺寸
        if "min-width: 50px" in content and "height: 24px" in content:
            fixes_checked.append("✅ 级别徽章固定尺寸已设置")
        else:
            fixes_checked.append("❌ 级别徽章固定尺寸未设置")
        
        # 检查统计信息对齐
        if "align-items: flex-end" in content:
            fixes_checked.append("✅ 统计信息右对齐已设置")
        else:
            fixes_checked.append("❌ 统计信息右对齐未设置")
        
        # 检查级别徽章颜色
        if "level-badge.level-首板" in content:
            fixes_checked.append("✅ 级别徽章颜色样式已添加")
        else:
            fixes_checked.append("❌ 级别徽章颜色样式缺失")
        
        for check in fixes_checked:
            print(f"   {check}")
        
        return len([c for c in fixes_checked if c.startswith("✅")]) >= 4
        
    except Exception as e:
        print(f"❌ 检查CSS文件失败: {e}")
        return False

def simulate_layout_calculation():
    """模拟布局计算，验证修复效果"""
    print("\n📊 模拟布局计算验证...")
    
    # 模拟测试数据
    test_scenarios = {
        "首板大量股票": {
            "level": "首板",
            "stock_count": 35,
            "container_height": 150
        },
        "1进2中等股票": {
            "level": "1进2", 
            "stock_count": 22,
            "container_height": 120
        },
        "2进3少量股票": {
            "level": "2进3",
            "stock_count": 15,
            "container_height": 100
        },
        "极小容器高度": {
            "level": "首板",
            "stock_count": 20,
            "container_height": 30  # 极小高度测试
        }
    }
    
    for scenario_name, scenario in test_scenarios.items():
        print(f"\n   🧪 测试场景: {scenario_name}")
        
        level = scenario["level"]
        stock_count = scenario["stock_count"]
        container_height = scenario["container_height"]
        
        # 模拟修复后的布局计算
        base_row_height = 20
        min_container_height = 60
        safe_container_height = max(container_height, min_container_height)
        max_rows = max(2, (safe_container_height - 15) // base_row_height)
        
        # 首板特殊处理
        if level in ["首板", "1进2"]:
            if stock_count <= 8:
                grid_cols = 4
                max_display_stocks = stock_count
            elif stock_count <= 20:
                grid_cols = 5
                max_display_stocks = min(stock_count, 20)
            elif stock_count <= 40:
                grid_cols = 6
                max_display_stocks = min(stock_count, 30)
            else:
                grid_cols = 6
                max_display_stocks = min(stock_count, 36)
        else:
            grid_cols = 4
            max_display_stocks = max_rows * grid_cols
        
        # 最终安全检查
        if max_display_stocks < 4 and stock_count > 0:
            max_display_stocks = min(stock_count, 4)
            grid_cols = min(grid_cols, stock_count)
        
        container_limit = max(12, max_rows * grid_cols)
        final_display_stocks = min(max_display_stocks, container_limit)
        
        print(f"     📏 容器高度: {container_height}px -> {safe_container_height}px")
        print(f"     📐 最大行数: {max_rows}")
        print(f"     🔢 网格列数: {grid_cols}")
        print(f"     📊 显示股票: {final_display_stocks}/{stock_count}")
        
        # 验证修复效果
        if level in ["首板", "1进2"] and final_display_stocks >= min(stock_count, 20):
            print(f"     ✅ {level}显示股票数量合理")
        elif final_display_stocks > 0:
            print(f"     ✅ 显示股票数量正常")
        else:
            print(f"     ❌ 显示股票数量异常")

def check_height_allocation():
    """检查高度分配算法"""
    print("\n📏 检查高度分配算法...")
    
    # 模拟数据
    total_height = 600
    stocks_data = {
        "首板": 35,
        "1进2": 22, 
        "2进3": 15,
        "3进4": 8,
        "4进5": 3
    }
    
    total_stocks = sum(stocks_data.values())
    allocated_heights = {}
    
    for level, stock_count in stocks_data.items():
        # 模拟修复后的高度分配
        base_height = 50
        row_height = 22
        
        if stock_count == 0:
            allocated_heights[level] = base_height
            continue
        
        # 首板特殊处理
        if level in ["首板", "1进2"]:
            if stock_count <= 10:
                special_height = base_height + (stock_count + 3) // 4 * row_height + 20
            elif stock_count <= 30:
                special_height = base_height + (stock_count + 4) // 5 * row_height + 30
            else:
                special_height = base_height + (stock_count + 5) // 6 * row_height + 40
            
            min_special_height = max(100, base_height + 3 * row_height)
            special_height = max(special_height, min_special_height)
            
            max_special_height = int(total_height * 0.6)
            special_height = min(special_height, max_special_height)
            
            allocated_heights[level] = special_height
        else:
            # 常规分配
            stock_ratio = stock_count / total_stocks
            base_allocation = int(total_height * stock_ratio * 1.2)
            
            if stock_count >= 20:
                density_weight = 1.4
            elif stock_count >= 10:
                density_weight = 1.2
            else:
                density_weight = 1.0
            
            adjusted_allocation = int(base_allocation * density_weight)
            
            if stock_count <= 4:
                min_height = base_height + ((stock_count + 1) // 2) * row_height + 10
            elif stock_count <= 12:
                min_height = base_height + ((stock_count + 3) // 4) * row_height + 15
            else:
                min_height = base_height + ((stock_count + 4) // 5) * row_height + 20
            
            final_height = max(adjusted_allocation, min_height)
            
            if stock_count >= 20:
                max_height = int(total_height * 0.6)
            elif stock_count >= 10:
                max_height = int(total_height * 0.5)
            else:
                max_height = int(total_height * 0.4)
            
            final_height = min(final_height, max_height)
            final_height = max(final_height, base_height + row_height)
            
            allocated_heights[level] = final_height
    
    print("   📊 高度分配结果:")
    total_allocated = 0
    for level, height in allocated_heights.items():
        stock_count = stocks_data[level]
        percentage = (height / total_height) * 100
        total_allocated += height
        print(f"     {level}: {height}px ({percentage:.1f}%) - {stock_count}只股票")
    
    print(f"   📈 总分配: {total_allocated}px / {total_height}px ({(total_allocated/total_height)*100:.1f}%)")
    
    # 验证首板分配是否合理
    shouban_height = allocated_heights.get("首板", 0)
    shouban_stocks = stocks_data.get("首板", 0)
    if shouban_height >= 100 and shouban_stocks > 0:
        print("   ✅ 首板高度分配合理")
        return True
    else:
        print("   ❌ 首板高度分配不足")
        return False

def generate_fix_report():
    """生成修复报告"""
    print("\n📋 生成紧急修复报告...")
    
    js_ok = check_javascript_fixes()
    css_ok = check_css_fixes()
    height_ok = check_height_allocation()
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "fix_version": "emergency_fix_09",
        "checks": {
            "javascript_fixes": js_ok,
            "css_fixes": css_ok,
            "height_allocation": height_ok
        },
        "overall_status": "success" if all([js_ok, css_ok, height_ok]) else "partial",
        "recommendations": []
    }
    
    if not js_ok:
        report["recommendations"].append("需要进一步检查JavaScript布局算法")
    if not css_ok:
        report["recommendations"].append("需要进一步检查CSS对齐样式")
    if not height_ok:
        report["recommendations"].append("需要进一步优化高度分配算法")
    
    # 保存报告
    with open("emergency_fix_09_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"   📄 修复报告已保存: emergency_fix_09_report.json")
    print(f"   🎯 整体状态: {'✅ 成功' if report['overall_status'] == 'success' else '⚠️ 部分成功'}")
    
    return report

def main():
    """主函数"""
    print("🚨 连板金字塔紧急修复验证 09")
    print("=" * 50)
    
    # 检查文件存在性
    required_files = [
        "static/js/charts.js",
        "static/css/style.css",
        "test_emergency_fix_09.html"
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 不存在")
            return
    
    # 执行各项检查
    simulate_layout_calculation()
    
    # 生成报告
    report = generate_fix_report()
    
    print("\n🎯 紧急修复验证完成!")
    print(f"修复状态: {'✅ 成功' if report['overall_status'] == 'success' else '⚠️ 需要进一步优化'}")
    
    if report["recommendations"]:
        print("\n📝 建议:")
        for rec in report["recommendations"]:
            print(f"   • {rec}")

if __name__ == "__main__":
    main()
