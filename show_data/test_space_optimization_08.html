<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>连板金字塔空间优化测试 08</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="static/css/style.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .test-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .test-info {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin-bottom: 20px;
        }

        .comparison-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .test-scenario {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .scenario-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
            padding: 5px 10px;
            background: #e9ecef;
            border-radius: 4px;
        }

        .pyramid-test-container {
            height: 500px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
        }
    </style>
</head>

<body>
    <div class="test-container">
        <h1 class="text-center mb-4">连板金字塔空间优化测试 08</h1>

        <div class="test-info">
            <h5>🎯 测试目标</h5>
            <ul>
                <li><strong>整体胜率模块压缩</strong>：减少高度占用，为连板数据腾出更多空间</li>
                <li><strong>智能空间分配</strong>：基于股票数量的动态空间分配算法</li>
                <li><strong>图标对齐优化</strong>：确保连板高度图标与胜率图标完美对齐</li>
                <li><strong>显示效果验证</strong>：测试不同股票数量分布下的显示效果</li>
            </ul>
        </div>

        <!-- 测试场景选择 -->
        <div class="test-card">
            <h5>📊 测试场景</h5>
            <div class="btn-group mb-3" role="group">
                <button type="button" class="btn btn-outline-primary active"
                    onclick="loadTestScenario('balanced')">均衡分布</button>
                <button type="button" class="btn btn-outline-primary"
                    onclick="loadTestScenario('heavy_low')">低级别密集</button>
                <button type="button" class="btn btn-outline-primary"
                    onclick="loadTestScenario('heavy_high')">高级别密集</button>
                <button type="button" class="btn btn-outline-primary"
                    onclick="loadTestScenario('extreme')">极端分布</button>
            </div>

            <div id="scenario-description" class="alert alert-info">
                <strong>均衡分布</strong>：各级别股票数量相对均衡，测试标准空间分配效果
            </div>
        </div>

        <!-- 金字塔显示区域 -->
        <div class="test-card">
            <h5>🏗️ 连板金字塔显示效果</h5>
            <div class="pyramid-test-container">
                <div class="pyramid-card-new" style="height: 100%;">
                    <div class="card-header py-2 text-center border-0 bg-gradient-to-r from-blue-50 to-purple-50">
                        <h6 class="mb-0 font-semibold text-gray-800">连板金字塔</h6>
                        <small class="text-gray-500" id="pyramid-date">加载中...</small>
                    </div>
                    <div class="card-body p-3">
                        <div class="pyramid-layout-fullwidth">
                            <div id="lianban-pyramid-visual" class="pyramid-container-fullwidth">
                                <div class="text-center">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2 text-muted">正在加载连板数据...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 优化效果分析 -->
        <div class="test-card">
            <h5>📈 优化效果分析</h5>
            <div id="optimization-analysis">
                <div class="row">
                    <div class="col-md-6">
                        <h6>空间分配统计</h6>
                        <div id="space-allocation-stats" class="small text-muted">
                            等待数据加载...
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>显示效果评估</h6>
                        <div id="display-effectiveness" class="small text-muted">
                            等待数据加载...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试日志 -->
        <div class="test-card">
            <h5>📝 测试日志</h5>
            <div id="test-log" class="small"
                style="max-height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 4px;">
                <div class="text-muted">等待测试开始...</div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="static/js/charts.js"></script>
    <script>
        // 测试场景数据
        const testScenarios = {
            balanced: {
                name: '均衡分布',
                description: '各级别股票数量相对均衡，测试标准空间分配效果',
                data: {
                    '首板': Array.from({ length: 25 }, (_, i) => ({
                        code: `SB${String(i + 1).padStart(3, '0')}`,
                        name: `首板股票${i + 1}`,
                        change_percent: (Math.random() * 20 - 10).toFixed(2),
                        status: Math.random() > 0.7 ? '成' : '败'
                    })),
                    '1进2': Array.from({ length: 18 }, (_, i) => ({
                        code: `12${String(i + 1).padStart(3, '0')}`,
                        name: `1进2股票${i + 1}`,
                        change_percent: (Math.random() * 15 - 5).toFixed(2),
                        status: Math.random() > 0.6 ? '成' : '败'
                    })),
                    '2进3': Array.from({ length: 12 }, (_, i) => ({
                        code: `23${String(i + 1).padStart(3, '0')}`,
                        name: `2进3股票${i + 1}`,
                        change_percent: (Math.random() * 12 - 3).toFixed(2),
                        status: Math.random() > 0.5 ? '成' : '败'
                    })),
                    '3进4': Array.from({ length: 6 }, (_, i) => ({
                        code: `34${String(i + 1).padStart(3, '0')}`,
                        name: `3进4股票${i + 1}`,
                        change_percent: (Math.random() * 10 - 2).toFixed(2),
                        status: Math.random() > 0.4 ? '成' : '败'
                    })),
                    '4进5': Array.from({ length: 3 }, (_, i) => ({
                        code: `45${String(i + 1).padStart(3, '0')}`,
                        name: `4进5股票${i + 1}`,
                        change_percent: (Math.random() * 8).toFixed(2),
                        status: Math.random() > 0.3 ? '成' : '败'
                    }))
                }
            },
            heavy_low: {
                name: '低级别密集',
                description: '首板和1进2股票数量很多，高级别较少，测试大量股票的空间分配',
                data: {
                    '首板': Array.from({ length: 45 }, (_, i) => ({
                        code: `SB${String(i + 1).padStart(3, '0')}`,
                        name: `首板股票${i + 1}`,
                        change_percent: (Math.random() * 20 - 10).toFixed(2),
                        status: Math.random() > 0.7 ? '成' : '败'
                    })),
                    '1进2': Array.from({ length: 32 }, (_, i) => ({
                        code: `12${String(i + 1).padStart(3, '0')}`,
                        name: `1进2股票${i + 1}`,
                        change_percent: (Math.random() * 15 - 5).toFixed(2),
                        status: Math.random() > 0.6 ? '成' : '败'
                    })),
                    '2进3': Array.from({ length: 8 }, (_, i) => ({
                        code: `23${String(i + 1).padStart(3, '0')}`,
                        name: `2进3股票${i + 1}`,
                        change_percent: (Math.random() * 12 - 3).toFixed(2),
                        status: Math.random() > 0.5 ? '成' : '败'
                    })),
                    '3进4': Array.from({ length: 2 }, (_, i) => ({
                        code: `34${String(i + 1).padStart(3, '0')}`,
                        name: `3进4股票${i + 1}`,
                        change_percent: (Math.random() * 10 - 2).toFixed(2),
                        status: Math.random() > 0.4 ? '成' : '败'
                    }))
                }
            },
            heavy_high: {
                name: '高级别密集',
                description: '高级别连板股票较多，低级别较少，测试高级别空间限制效果',
                data: {
                    '首板': Array.from({ length: 8 }, (_, i) => ({
                        code: `SB${String(i + 1).padStart(3, '0')}`,
                        name: `首板股票${i + 1}`,
                        change_percent: (Math.random() * 20 - 10).toFixed(2),
                        status: Math.random() > 0.7 ? '成' : '败'
                    })),
                    '1进2': Array.from({ length: 12 }, (_, i) => ({
                        code: `12${String(i + 1).padStart(3, '0')}`,
                        name: `1进2股票${i + 1}`,
                        change_percent: (Math.random() * 15 - 5).toFixed(2),
                        status: Math.random() > 0.6 ? '成' : '败'
                    })),
                    '2进3': Array.from({ length: 15 }, (_, i) => ({
                        code: `23${String(i + 1).padStart(3, '0')}`,
                        name: `2进3股票${i + 1}`,
                        change_percent: (Math.random() * 12 - 3).toFixed(2),
                        status: Math.random() > 0.5 ? '成' : '败'
                    })),
                    '3进4': Array.from({ length: 18 }, (_, i) => ({
                        code: `34${String(i + 1).padStart(3, '0')}`,
                        name: `3进4股票${i + 1}`,
                        change_percent: (Math.random() * 10 - 2).toFixed(2),
                        status: Math.random() > 0.4 ? '成' : '败'
                    })),
                    '4进5': Array.from({ length: 12 }, (_, i) => ({
                        code: `45${String(i + 1).padStart(3, '0')}`,
                        name: `4进5股票${i + 1}`,
                        change_percent: (Math.random() * 8).toFixed(2),
                        status: Math.random() > 0.3 ? '成' : '败'
                    })),
                    '5进6': Array.from({ length: 8 }, (_, i) => ({
                        code: `56${String(i + 1).padStart(3, '0')}`,
                        name: `5进6股票${i + 1}`,
                        change_percent: (Math.random() * 6).toFixed(2),
                        status: Math.random() > 0.2 ? '成' : '败'
                    }))
                }
            },
            extreme: {
                name: '极端分布',
                description: '极端的股票数量分布，测试算法的鲁棒性',
                data: {
                    '首板': Array.from({ length: 60 }, (_, i) => ({
                        code: `SB${String(i + 1).padStart(3, '0')}`,
                        name: `首板股票${i + 1}`,
                        change_percent: (Math.random() * 20 - 10).toFixed(2),
                        status: Math.random() > 0.7 ? '成' : '败'
                    })),
                    '1进2': Array.from({ length: 2 }, (_, i) => ({
                        code: `12${String(i + 1).padStart(3, '0')}`,
                        name: `1进2股票${i + 1}`,
                        change_percent: (Math.random() * 15 - 5).toFixed(2),
                        status: Math.random() > 0.6 ? '成' : '败'
                    })),
                    '2进3': Array.from({ length: 1 }, (_, i) => ({
                        code: `23${String(i + 1).padStart(3, '0')}`,
                        name: `2进3股票${i + 1}`,
                        change_percent: (Math.random() * 12 - 3).toFixed(2),
                        status: Math.random() > 0.5 ? '成' : '败'
                    })),
                    '4进5': Array.from({ length: 25 }, (_, i) => ({
                        code: `45${String(i + 1).padStart(3, '0')}`,
                        name: `4进5股票${i + 1}`,
                        change_percent: (Math.random() * 8).toFixed(2),
                        status: Math.random() > 0.3 ? '成' : '败'
                    }))
                }
            }
        };

        // 当前测试场景
        let currentScenario = 'balanced';

        // 加载测试场景
        function loadTestScenario(scenarioKey) {
            currentScenario = scenarioKey;
            const scenario = testScenarios[scenarioKey];

            // 更新按钮状态
            document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // 更新描述
            document.getElementById('scenario-description').innerHTML =
                `<strong>${scenario.name}</strong>：${scenario.description}`;

            // 模拟数据加载
            loadTestData(scenario.data);

            logTest(`切换到测试场景: ${scenario.name}`);
        }

        // 加载测试数据
        function loadTestData(stocksData) {
            // 模拟进度数据
            const progressData = {};
            Object.keys(stocksData).forEach(level => {
                const stocks = stocksData[level];
                const successCount = stocks.filter(s => s.status === '成').length;
                const totalCount = stocks.length;
                progressData[level] = {
                    success: successCount,
                    total: totalCount,
                    percentage: totalCount > 0 ? Math.round((successCount / totalCount) * 100) : 0
                };
            });

            // 更新金字塔显示
            updateModernPyramid(progressData, stocksData);

            // 更新分析数据
            updateAnalysis(stocksData, progressData);

            logTest(`加载测试数据完成，共 ${Object.keys(stocksData).length} 个级别`);
        }

        // 更新分析数据
        function updateAnalysis(stocksData, progressData) {
            // 空间分配统计
            let spaceStats = '<div class="small">';
            let totalStocks = 0;
            Object.values(stocksData).forEach(stocks => totalStocks += stocks.length);

            Object.keys(stocksData).forEach(level => {
                const count = stocksData[level].length;
                const percentage = ((count / totalStocks) * 100).toFixed(1);
                spaceStats += `<div>${level}: ${count}只 (${percentage}%)</div>`;
            });
            spaceStats += `<div class="mt-2"><strong>总计: ${totalStocks}只股票</strong></div></div>`;

            document.getElementById('space-allocation-stats').innerHTML = spaceStats;

            // 显示效果评估
            let effectiveness = '<div class="small">';
            Object.keys(progressData).forEach(level => {
                const data = progressData[level];
                effectiveness += `<div>${level}: ${data.success}/${data.total} (${data.percentage}%)</div>`;
            });
            effectiveness += '</div>';

            document.getElementById('display-effectiveness').innerHTML = effectiveness;
        }

        // 测试日志
        function logTest(message) {
            const logContainer = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span class="text-muted">[${timestamp}]</span> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function () {
            logTest('测试页面初始化完成');
            loadTestScenario('balanced');
        });
    </script>
</body>

</html>