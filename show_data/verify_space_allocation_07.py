#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连板金字塔空间分配优化验证脚本07
验证基于股票数量的智能空间分配策略和图标对齐优化
"""

import os
import json
import time
import requests
from datetime import datetime

def print_header(title):
    """打印标题"""
    print(f"\n{'='*60}")
    print(f"🎯 {title}")
    print(f"{'='*60}")

def print_section(title):
    """打印章节"""
    print(f"\n📋 {title}")
    print("-" * 40)

def check_server_status():
    """检查服务器状态"""
    print_section("检查服务器状态")
    
    try:
        response = requests.get("http://localhost:8080/api/lianban_progress", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 服务器运行正常")
            print(f"📊 数据状态: {'成功' if data.get('success') else '失败'}")
            return True, data
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False, None
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return False, None

def analyze_space_allocation_strategy():
    """分析空间分配策略"""
    print_section("分析空间分配策略")
    
    try:
        with open("static/js/charts.js", "r", encoding="utf-8") as f:
            content = f.read()
            
        # 检查关键优化点
        optimizations = [
            ("基于股票数量的空间分配", "stockRatio = stockCount / totalStocks"),
            ("级别权重调整", "levelWeight = 1."),
            ("最小高度保证", "minRequiredHeight"),
            ("最大高度限制", "maxAllowedHeight"),
            ("动态列数布局", "Math.ceil(stockCount /"),
            ("智能空间计算", "baseAllocation * levelWeight"),
        ]
        
        for desc, code_snippet in optimizations:
            if code_snippet in content:
                print(f"✅ {desc}: 已实现")
            else:
                print(f"❌ {desc}: 未找到")
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def analyze_css_optimizations():
    """分析CSS样式优化"""
    print_section("分析CSS样式优化")
    
    try:
        with open("static/css/style.css", "r", encoding="utf-8") as f:
            content = f.read()
            
        # 检查样式优化
        css_optimizations = [
            ("整体胜率区域优化", ".overall-success-rate"),
            ("图标对齐优化", "align-items: center"),
            ("固定高度确保对齐", "height: 24px"),
            ("减少间距优化", "margin-bottom: 3px"),
            ("级别徽章对齐", ".level-badge"),
            ("胜率文本对齐", ".success-rate-text"),
        ]
        
        for desc, css_snippet in css_optimizations:
            if css_snippet in content:
                print(f"✅ {desc}: 已优化")
            else:
                print(f"❌ {desc}: 未找到")
                
    except Exception as e:
        print(f"❌ CSS检查失败: {e}")

def simulate_space_allocation(data):
    """模拟空间分配计算"""
    print_section("模拟空间分配计算")
    
    if not data or not data.get('success'):
        print("❌ 无有效数据进行模拟")
        return
        
    try:
        # 获取股票数据
        response = requests.get("http://localhost:8080/api/lianban_progress", timeout=5)
        result = response.json()
        
        if not result.get('latest_stocks'):
            print("❌ 无股票数据")
            return
            
        stocks_data = result['latest_stocks']
        
        # 计算总股票数
        total_stocks = sum(len(stocks) for stocks in stocks_data.values())
        total_height = 500  # 假设总高度
        
        print(f"📊 总股票数: {total_stocks}")
        print(f"📏 总可用高度: {total_height}px")
        print()
        
        # 按级别分析
        levels = ['5进6', '4进5', '3进4', '2进3', '1进2', '首板']
        
        for level in levels:
            if level not in stocks_data:
                continue
                
            stocks = stocks_data[level]
            stock_count = len(stocks)
            
            if stock_count == 0:
                continue
                
            # 计算空间分配
            stock_ratio = stock_count / total_stocks
            
            # 级别权重
            if level in ['5进6', '4进5']:
                level_weight = 1.2
                category = "超高级别"
            elif level in ['3进4']:
                level_weight = 1.1
                category = "高级别"
            elif level == '2进3':
                level_weight = 1.0
                category = "中级别"
            else:
                level_weight = 0.9
                category = "低级别"
                
            # 基础分配
            base_allocation = int(total_height * stock_ratio)
            adjusted_allocation = int(base_allocation * level_weight)
            
            # 最小高度需求
            if stock_count <= 4:
                min_height = 40 + (stock_count + 1) // 2 * 22 + 10
            elif stock_count <= 12:
                min_height = 40 + (stock_count + 3) // 4 * 22 + 15
            else:
                min_height = 40 + (stock_count + 4) // 5 * 22 + 20
                
            final_height = max(adjusted_allocation, min_height)
            
            print(f"🔸 {level} ({category})")
            print(f"   股票数量: {stock_count}只 ({stock_ratio*100:.1f}%)")
            print(f"   级别权重: {level_weight}")
            print(f"   基础分配: {base_allocation}px")
            print(f"   调整分配: {adjusted_allocation}px")
            print(f"   最小需求: {min_height}px")
            print(f"   最终高度: {final_height}px")
            print()
            
    except Exception as e:
        print(f"❌ 模拟计算失败: {e}")

def check_test_page_accessibility():
    """检查测试页面可访问性"""
    print_section("检查测试页面可访问性")
    
    test_urls = [
        ("空间分配优化测试07", "http://localhost:8080/test_space_allocation_07.html"),
        ("主页面", "http://localhost:8080/"),
        ("智能显示测试06", "http://localhost:8080/test_pyramid_smart_06.html"),
    ]
    
    for name, url in test_urls:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {name}: 可访问")
            else:
                print(f"❌ {name}: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {name}: 连接失败 - {e}")

def generate_optimization_report():
    """生成优化报告"""
    print_section("生成优化报告")
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "optimization_type": "基于股票数量的智能空间分配",
        "key_improvements": [
            "根据股票数量占比分配基础空间",
            "应用级别权重调整(超高级别1.2, 高级别1.1, 中级别1.0, 低级别0.9)",
            "确保最小高度需求满足",
            "设置合理的最大高度限制",
            "优化整体胜率区域占用空间",
            "统一图标高度确保对齐",
            "减少间距为更多股票腾出空间"
        ],
        "expected_benefits": [
            "股票数量多的级别获得更多显示空间",
            "高级别连板保持重要性权重",
            "整体空间利用率提升",
            "视觉对齐效果改善",
            "用户体验优化"
        ]
    }
    
    # 保存报告
    report_file = f"optimization_report_07_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
        
    print(f"📄 优化报告已保存: {report_file}")
    
    # 打印关键信息
    print("\n🎯 关键优化点:")
    for improvement in report["key_improvements"]:
        print(f"   • {improvement}")
        
    print("\n🚀 预期效果:")
    for benefit in report["expected_benefits"]:
        print(f"   • {benefit}")

def main():
    """主函数"""
    print_header("连板金字塔空间分配优化验证07")
    print(f"⏰ 验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 检查服务器状态
    server_ok, data = check_server_status()
    
    if not server_ok:
        print("\n❌ 服务器未运行，请先启动服务器")
        return
    
    # 2. 分析空间分配策略
    analyze_space_allocation_strategy()
    
    # 3. 分析CSS优化
    analyze_css_optimizations()
    
    # 4. 模拟空间分配
    simulate_space_allocation(data)
    
    # 5. 检查测试页面
    check_test_page_accessibility()
    
    # 6. 生成优化报告
    generate_optimization_report()
    
    print_header("验证完成")
    print("🎉 空间分配优化验证已完成！")
    print("📱 请在浏览器中查看以下页面对比效果:")
    print("   • http://localhost:8080/test_space_allocation_07.html (测试页面)")
    print("   • http://localhost:8080/ (主页面)")

if __name__ == "__main__":
    main()
