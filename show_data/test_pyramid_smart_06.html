<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>连板金字塔智能显示测试06</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style.css">
    <style>
        body {
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-title {
            color: #1f2937;
            font-weight: 600;
            margin-bottom: 15px;
        }
        .status {
            padding: 8px 12px;
            border-radius: 6px;
            margin: 5px 0;
            font-weight: 500;
        }
        .status.success { background: #d1fae5; color: #065f46; }
        .status.error { background: #fee2e2; color: #991b1b; }
        .status.warning { background: #fef3c7; color: #92400e; }
        .status.info { background: #dbeafe; color: #1e40af; }
        .strategy-card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .strategy-card.high-level {
            border-color: #dc2626;
            background: #fef2f2;
        }
        .strategy-card.mid-level {
            border-color: #d97706;
            background: #fffbeb;
        }
        .strategy-card.low-level {
            border-color: #059669;
            background: #ecfdf5;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-card">
            <h3 class="test-title">连板金字塔智能显示策略测试06</h3>
            <p>测试高级别连板优先显示、低级别连板智能折叠的优化策略</p>
            
            <div id="test-status" class="status info">🔄 开始测试...</div>
        </div>
        
        <!-- 显示策略说明 -->
        <div class="test-card">
            <h5>智能显示策略</h5>
            <div class="row">
                <div class="col-md-4">
                    <div class="strategy-card high-level">
                        <h6 class="text-danger">🔥 高级别连板 (3进4及以上)</h6>
                        <ul class="small">
                            <li>优先完整显示所有股票</li>
                            <li>获得更多显示空间</li>
                            <li>使用较大字体和宽松布局</li>
                            <li>最多占用50%总高度</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="strategy-card mid-level">
                        <h6 class="text-warning">📈 中级别连板 (2进3)</h6>
                        <ul class="small">
                            <li>适度显示策略</li>
                            <li>中等数量完整显示</li>
                            <li>大量时适当限制</li>
                            <li>最多占用35%总高度</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="strategy-card low-level">
                        <h6 class="text-success">📊 低级别连板 (首板、1进2)</h6>
                        <ul class="small">
                            <li>智能折叠显示</li>
                            <li>优先显示成功和高涨幅股票</li>
                            <li>大量时严格限制为3行</li>
                            <li>最多占用25%总高度</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 连板金字塔显示区域 -->
        <div class="test-card">
            <h5>连板金字塔显示效果</h5>
            <div class="pyramid-card-new" style="height: 600px;">
                <div class="card-header py-2 text-center border-0 bg-gradient-to-r from-blue-50 to-purple-50">
                    <h6 class="mb-0 font-semibold text-gray-800">连板金字塔</h6>
                    <small class="text-gray-500" id="pyramid-date">加载中...</small>
                </div>
                <div class="card-body p-3">
                    <div class="pyramid-layout-fullwidth">
                        <div id="lianban-pyramid-visual" class="pyramid-container-fullwidth">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2 text-muted">正在加载连板数据...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 测试结果分析 -->
        <div class="test-card">
            <h5>测试结果分析</h5>
            <div id="analysis-results">
                <div class="text-muted">等待数据加载完成...</div>
            </div>
        </div>
        
        <!-- 控制台日志显示 -->
        <div class="test-card">
            <h5>控制台日志</h5>
            <div id="console-logs" style="background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace; font-size: 0.85rem; max-height: 300px; overflow-y: auto;">
                <div>等待日志输出...</div>
            </div>
        </div>
    </div>

    <!-- 引入必要的JavaScript文件 -->
    <script src="/static/js/charts.js"></script>
    
    <script>
        // 捕获控制台日志
        const originalLog = console.log;
        const logsContainer = document.getElementById('console-logs');
        
        function addLogToDisplay(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.createElement('div');
            logDiv.style.marginBottom = '5px';
            logDiv.innerHTML = `[${timestamp}] ${message}`;
            logsContainer.appendChild(logDiv);
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLogToDisplay(args.join(' '));
        };
        
        // 分析连板数据并生成报告
        function analyzeDisplayStrategy(data, stocksData) {
            const analysisContainer = document.getElementById('analysis-results');
            let analysisHtml = '<div class="row">';
            
            if (!stocksData) {
                analysisHtml += '<div class="col-12"><div class="alert alert-warning">暂无股票数据进行分析</div></div>';
                analysisContainer.innerHTML = analysisHtml + '</div>';
                return;
            }
            
            const levels = Object.keys(stocksData).sort((a, b) => {
                const getPriority = (level) => {
                    if (level === '首板') return 0;
                    if (level === '1进2') return 1;
                    if (level === '2进3') return 2;
                    const match = level.match(/(\d+)进(\d+)/);
                    return match ? parseInt(match[1]) : 0;
                };
                return getPriority(b) - getPriority(a);
            });
            
            levels.forEach(level => {
                const stocks = stocksData[level] || [];
                const priority = getLevelPriority ? getLevelPriority(level) : 0;
                let categoryClass = 'low-level';
                let categoryName = '低级别';
                
                if (priority >= 3) {
                    categoryClass = 'high-level';
                    categoryName = '高级别';
                } else if (priority === 2) {
                    categoryClass = 'mid-level';
                    categoryName = '中级别';
                }
                
                analysisHtml += `
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="strategy-card ${categoryClass}">
                        <h6>${level} (${categoryName})</h6>
                        <div class="small">
                            <div>股票数量: <strong>${stocks.length}</strong></div>
                            <div>优先级: <strong>${priority}</strong></div>
                            <div>预期策略: ${priority >= 3 ? '完整显示' : priority === 2 ? '适度显示' : '智能折叠'}</div>
                        </div>
                    </div>
                </div>
                `;
            });
            
            analysisHtml += '</div>';
            analysisContainer.innerHTML = analysisHtml;
        }
        
        // 页面加载完成后开始测试
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 页面加载完成，开始连板金字塔智能显示测试...');
            
            const testStatus = document.getElementById('test-status');
            
            try {
                testStatus.className = 'status info';
                testStatus.textContent = '📡 正在加载连板数据...';
                
                // 加载连板数据
                const response = await fetch('/api/lianban_progress');
                const result = await response.json();
                
                if (result.success) {
                    console.log('✅ 连板数据加载成功');
                    testStatus.className = 'status success';
                    testStatus.textContent = '✅ 数据加载成功，正在应用智能显示策略...';
                    
                    // 更新金字塔显示
                    if (typeof updateModernPyramid === 'function') {
                        updateModernPyramid(result.data, result.latest_stocks);
                        
                        // 分析显示策略
                        setTimeout(() => {
                            analyzeDisplayStrategy(result.data, result.latest_stocks);
                        }, 1000);
                        
                        testStatus.className = 'status success';
                        testStatus.textContent = '🎉 智能显示策略测试完成！';
                    } else {
                        throw new Error('updateModernPyramid函数未定义');
                    }
                } else {
                    throw new Error(result.error || '数据加载失败');
                }
            } catch (error) {
                console.error('❌ 测试失败:', error);
                testStatus.className = 'status error';
                testStatus.textContent = `❌ 测试失败: ${error.message}`;
            }
        });
    </script>
</body>
</html>
