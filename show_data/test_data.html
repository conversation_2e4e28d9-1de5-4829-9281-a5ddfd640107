<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .data-display { background: #f5f5f5; padding: 10px; margin: 10px 0; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
        button { padding: 10px 15px; margin: 5px; }
    </style>
</head>
<body>
    <h1>股票数据API测试</h1>
    
    <div class="section">
        <h2>历史市场数据</h2>
        <button onclick="loadMarketSummary()">加载市场概况</button>
        <div id="market-summary" class="data-display">点击按钮加载数据...</div>
    </div>
    
    <div class="section">
        <h2>历史涨跌停数据</h2>
        <button onclick="loadLimitStats()">加载涨跌停统计</button>
        <div id="limit-stats" class="data-display">点击按钮加载数据...</div>
    </div>
    
    <div class="section">
        <h2>实时市场数据</h2>
        <button onclick="loadRealtimeMarket()">加载实时市场数据</button>
        <div id="realtime-market" class="data-display">点击按钮加载数据...</div>
    </div>
    
    <div class="section">
        <h2>实时数据历史</h2>
        <button onclick="loadRealtimeHistory()">加载实时数据历史</button>
        <div id="realtime-history" class="data-display">点击按钮加载数据...</div>
    </div>

    <script>
        async function loadMarketSummary() {
            try {
                const response = await fetch('/api/market_summary');
                const result = await response.json();
                document.getElementById('market-summary').innerHTML = 
                    '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('market-summary').innerHTML = 
                    '<div style="color: red;">错误: ' + error.message + '</div>';
            }
        }

        async function loadLimitStats() {
            try {
                const response = await fetch('/api/limit_stats');
                const result = await response.json();
                document.getElementById('limit-stats').innerHTML = 
                    '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('limit-stats').innerHTML = 
                    '<div style="color: red;">错误: ' + error.message + '</div>';
            }
        }

        async function loadRealtimeMarket() {
            try {
                const response = await fetch('/api/realtime/market_summary');
                const result = await response.json();
                document.getElementById('realtime-market').innerHTML = 
                    '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('realtime-market').innerHTML = 
                    '<div style="color: red;">错误: ' + error.message + '</div>';
            }
        }

        async function loadRealtimeHistory() {
            try {
                const response = await fetch('/api/realtime/history/market');
                const result = await response.json();
                document.getElementById('realtime-history').innerHTML = 
                    '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('realtime-history').innerHTML = 
                    '<div style="color: red;">错误: ' + error.message + '</div>';
            }
        }
    </script>
</body>
</html>
