<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>连板金字塔修复验证</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style.css">
    <style>
        body {
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .test-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #22c55e; }
        .status-error { background-color: #ef4444; }
        .status-loading { background-color: #f59e0b; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-card">
            <h3>🎯 连板金字塔修复验证</h3>
            <p class="text-muted">验证股票显示问题和胜率统计功能是否正常工作</p>
            
            <!-- 状态指示器 -->
            <div class="mb-3">
                <h5>修复状态检查</h5>
                <div id="status-api" class="mb-2">
                    <span class="status-indicator status-loading"></span>
                    <span>API数据加载</span>
                    <span id="api-result" class="ms-2 text-muted">检查中...</span>
                </div>
                <div id="status-pyramid" class="mb-2">
                    <span class="status-indicator status-loading"></span>
                    <span>金字塔渲染</span>
                    <span id="pyramid-result" class="ms-2 text-muted">检查中...</span>
                </div>
                <div id="status-stocks" class="mb-2">
                    <span class="status-indicator status-loading"></span>
                    <span>股票显示</span>
                    <span id="stocks-result" class="ms-2 text-muted">检查中...</span>
                </div>
                <div id="status-rates" class="mb-2">
                    <span class="status-indicator status-loading"></span>
                    <span>胜率统计</span>
                    <span id="rates-result" class="ms-2 text-muted">检查中...</span>
                </div>
            </div>
        </div>
        
        <!-- 金字塔显示区域 -->
        <div class="test-card">
            <h5>连板金字塔显示</h5>
            <div class="pyramid-card-new" style="height: 500px;">
                <div class="card-header py-2 text-center border-0 bg-gradient-to-r from-blue-50 to-purple-50">
                    <h6 class="mb-0 font-semibold text-gray-800">连板金字塔</h6>
                    <small class="text-gray-500" id="pyramid-date">加载中...</small>
                </div>
                <div class="card-body p-3">
                    <div class="pyramid-layout-fullwidth">
                        <div id="lianban-pyramid-visual" class="pyramid-container-fullwidth">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2 text-muted">正在加载连板数据...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 测试结果 -->
        <div class="test-card">
            <h5>测试结果详情</h5>
            <div id="test-details" class="mt-3">
                <p class="text-muted">正在运行测试...</p>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/utils.js"></script>
    <script src="/static/js/charts.js"></script>
    
    <script>
        let testResults = {
            api: false,
            pyramid: false,
            stocks: false,
            rates: false
        };
        
        function updateStatus(type, success, message) {
            const indicator = document.querySelector(`#status-${type} .status-indicator`);
            const result = document.getElementById(`${type}-result`);
            
            indicator.className = `status-indicator ${success ? 'status-success' : 'status-error'}`;
            result.textContent = message;
            result.className = `ms-2 ${success ? 'text-success' : 'text-danger'}`;
            
            testResults[type] = success;
            updateOverallStatus();
        }
        
        function updateOverallStatus() {
            const allPassed = Object.values(testResults).every(result => result);
            const details = document.getElementById('test-details');
            
            if (allPassed) {
                details.innerHTML = `
                    <div class="alert alert-success">
                        <h6>✅ 所有测试通过！</h6>
                        <ul class="mb-0">
                            <li>API数据正常加载</li>
                            <li>金字塔成功渲染</li>
                            <li>股票显示无重叠</li>
                            <li>胜率统计正确显示</li>
                        </ul>
                        <p class="mt-2 mb-0"><strong>修复完成！</strong>连板金字塔模块现在可以正常工作了。</p>
                    </div>
                `;
            }
        }
        
        async function runTests() {
            try {
                // 测试1: API数据加载
                console.log('🧪 测试API数据加载...');
                const response = await fetch('/api/lianban_progress');
                const data = await response.json();
                
                if (data.success && data.data && data.data.length > 0) {
                    updateStatus('api', true, `成功 (${data.data.length}天数据)`);
                } else {
                    updateStatus('api', false, '数据无效');
                    return;
                }
                
                // 测试2: 金字塔渲染
                console.log('🧪 测试金字塔渲染...');
                updateModernPyramid(data.data, data.latest_stocks);
                
                // 等待渲染完成
                setTimeout(() => {
                    const pyramidElement = document.getElementById('lianban-pyramid-visual');
                    const hasContent = pyramidElement && pyramidElement.innerHTML.includes('lianban-level-row');
                    
                    if (hasContent) {
                        updateStatus('pyramid', true, '渲染成功');
                        
                        // 测试3: 股票显示
                        const stockItems = pyramidElement.querySelectorAll('.stock-item');
                        if (stockItems.length > 0) {
                            updateStatus('stocks', true, `显示${stockItems.length}只股票`);
                        } else {
                            updateStatus('stocks', false, '无股票显示');
                        }
                        
                        // 测试4: 胜率统计
                        const rateElements = pyramidElement.querySelectorAll('.success-rate-text');
                        if (rateElements.length > 0) {
                            updateStatus('rates', true, `显示${rateElements.length}个胜率`);
                        } else {
                            updateStatus('rates', false, '无胜率显示');
                        }
                        
                    } else {
                        updateStatus('pyramid', false, '渲染失败');
                    }
                }, 1000);
                
            } catch (error) {
                console.error('测试失败:', error);
                updateStatus('api', false, `错误: ${error.message}`);
            }
        }
        
        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 开始运行修复验证测试...');
            setTimeout(runTests, 1000);
        });
    </script>
</body>
</html>
