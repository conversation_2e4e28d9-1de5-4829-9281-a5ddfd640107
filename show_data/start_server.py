#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动Web服务器的脚本
"""

import os
import sys

# 添加父目录到路径，以便导入其他模块
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from app import app

if __name__ == '__main__':
    port = 5001  # 使用不同的端口避免冲突
    print("🚀 启动股票数据可视化Web服务器")
    print("=" * 50)
    print(f"📊 访问地址: http://localhost:{port}")
    print(f"🧪 测试页面: http://localhost:{port}/test_data.html")
    print("=" * 50)

    # 启动服务器
    app.run(
        debug=True,
        host='0.0.0.0',
        port=port,
        threaded=True
    )
