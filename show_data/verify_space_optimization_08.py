#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连板金字塔空间优化验证脚本 08
验证基于股票数量的智能空间分配策略
"""

import json
import os
from datetime import datetime

def verify_space_allocation_algorithm():
    """验证空间分配算法的效果"""
    print("🔍 验证连板金字塔空间优化效果...")
    
    # 测试场景数据
    test_scenarios = {
        "均衡分布": {
            "首板": 25, "1进2": 18, "2进3": 12, "3进4": 6, "4进5": 3
        },
        "低级别密集": {
            "首板": 45, "1进2": 32, "2进3": 8, "3进4": 2
        },
        "高级别密集": {
            "首板": 8, "1进2": 12, "2进3": 15, "3进4": 18, "4进5": 12, "5进6": 8
        },
        "极端分布": {
            "首板": 60, "1进2": 2, "2进3": 1, "4进5": 25
        }
    }
    
    print("\n📊 测试场景分析:")
    for scenario_name, stocks_data in test_scenarios.items():
        print(f"\n🎯 {scenario_name}:")
        total_stocks = sum(stocks_data.values())
        print(f"   总股票数: {total_stocks}")
        
        # 模拟空间分配
        total_height = 400  # 假设总高度400px
        allocated_heights = simulate_space_allocation(stocks_data, total_height)
        
        # 分析分配效果
        print("   空间分配结果:")
        for level, count in stocks_data.items():
            height = allocated_heights.get(level, 0)
            percentage = (height / total_height) * 100
            stock_ratio = (count / total_stocks) * 100
            efficiency = percentage / stock_ratio if stock_ratio > 0 else 0
            
            print(f"     {level}: {count}只股票 -> {height}px ({percentage:.1f}%) "
                  f"[股票占比{stock_ratio:.1f}%, 效率{efficiency:.2f}]")
        
        # 评估分配合理性
        evaluate_allocation_efficiency(stocks_data, allocated_heights, total_height)

def simulate_space_allocation(stocks_data, total_height):
    """模拟空间分配算法"""
    base_height = 35
    row_height = 20
    total_stocks = sum(stocks_data.values())
    allocated_heights = {}
    
    for level, stock_count in stocks_data.items():
        if stock_count == 0:
            allocated_heights[level] = base_height
            continue
        
        # 计算股票数量占比
        stock_ratio = stock_count / total_stocks
        
        # 基础分配（增加10%）
        base_allocation = int(total_height * stock_ratio * 1.1)
        
        # 股票密度权重
        if stock_count >= 30:
            density_weight = 1.5
        elif stock_count >= 20:
            density_weight = 1.3
        elif stock_count >= 10:
            density_weight = 1.1
        elif stock_count >= 5:
            density_weight = 1.0
        else:
            density_weight = 0.8
        
        # 级别权重
        level_priority = get_level_priority(level)
        if level_priority >= 5:
            level_weight = 1.1
        elif level_priority >= 3:
            level_weight = 1.0
        elif level_priority == 2:
            level_weight = 1.05
        else:
            level_weight = 1.0
        
        # 应用权重
        adjusted_allocation = int(base_allocation * density_weight * level_weight)
        
        # 计算最小需求高度
        if stock_count <= 4:
            min_height = base_height + ((stock_count + 1) // 2) * row_height + 8
        elif stock_count <= 12:
            min_height = base_height + ((stock_count + 3) // 4) * row_height + 12
        elif stock_count <= 30:
            min_height = base_height + ((stock_count + 4) // 5) * row_height + 16
        else:
            min_height = base_height + ((stock_count + 5) // 6) * row_height + 20
        
        # 确保不少于最小需求
        final_height = max(adjusted_allocation, min_height)
        
        # 设置上限
        if stock_count >= 30:
            max_height = int(total_height * 0.65)
        elif stock_count >= 20:
            max_height = int(total_height * 0.50)
        elif stock_count >= 10:
            max_height = int(total_height * 0.40)
        elif stock_count >= 5:
            max_height = int(total_height * 0.30)
        else:
            max_height = int(total_height * 0.25)
        
        final_height = min(final_height, max_height)
        final_height = max(final_height, base_height + row_height)
        
        allocated_heights[level] = final_height
    
    return allocated_heights

def get_level_priority(level):
    """获取连板级别优先级"""
    if level == '首板':
        return 0
    elif level == '1进2':
        return 1
    elif level == '2进3':
        return 2
    elif level.endswith('进4'):
        return 3
    elif level.endswith('进5'):
        return 4
    elif level.endswith('进6'):
        return 5
    else:
        return 0

def evaluate_allocation_efficiency(stocks_data, allocated_heights, total_height):
    """评估分配效率"""
    total_stocks = sum(stocks_data.values())
    total_allocated = sum(allocated_heights.values())
    
    print(f"   📈 分配效率评估:")
    print(f"     总分配高度: {total_allocated}px / {total_height}px ({(total_allocated/total_height)*100:.1f}%)")
    
    # 计算股票密度效率
    high_density_levels = [level for level, count in stocks_data.items() if count >= 20]
    if high_density_levels:
        high_density_allocation = sum(allocated_heights.get(level, 0) for level in high_density_levels)
        high_density_stocks = sum(stocks_data[level] for level in high_density_levels)
        high_density_ratio = (high_density_stocks / total_stocks) * 100
        high_density_space_ratio = (high_density_allocation / total_allocated) * 100
        
        print(f"     高密度级别: {len(high_density_levels)}个级别, {high_density_stocks}只股票 ({high_density_ratio:.1f}%)")
        print(f"     高密度空间: {high_density_allocation}px ({high_density_space_ratio:.1f}%)")
        
        if high_density_space_ratio >= high_density_ratio:
            print(f"     ✅ 高密度级别获得了充足的空间分配")
        else:
            print(f"     ⚠️  高密度级别空间分配可能不足")
    
    # 检查空间利用率
    utilization = (total_allocated / total_height) * 100
    if utilization > 95:
        print(f"     ✅ 空间利用率优秀 ({utilization:.1f}%)")
    elif utilization > 85:
        print(f"     ✅ 空间利用率良好 ({utilization:.1f}%)")
    else:
        print(f"     ⚠️  空间利用率有待提升 ({utilization:.1f}%)")

def check_css_optimization():
    """检查CSS优化效果"""
    print("\n🎨 检查CSS样式优化...")

    css_file = "static/css/style.css"
    if not os.path.exists(css_file):
        print("❌ CSS文件不存在")
        return False
    
    with open(css_file, 'r', encoding='utf-8') as f:
        css_content = f.read()
    
    # 检查整体胜率样式优化
    optimizations = {
        "整体胜率压缩": "min-height: 22px" in css_content,
        "减少内边距": "padding: 3px 8px" in css_content,
        "紧凑行高": "line-height: 1.1" in css_content,
        "级别行优化": "min-height: 38px" in css_content,
        "对齐优化": "align-items: center" in css_content
    }
    
    print("   CSS优化检查结果:")
    for optimization, status in optimizations.items():
        status_icon = "✅" if status else "❌"
        print(f"     {status_icon} {optimization}: {'已优化' if status else '未优化'}")
    
    return all(optimizations.values())

def check_js_optimization():
    """检查JavaScript优化效果"""
    print("\n⚙️ 检查JavaScript算法优化...")

    js_file = "static/js/charts.js"
    if not os.path.exists(js_file):
        print("❌ JavaScript文件不存在")
        return False
    
    with open(js_file, 'r', encoding='utf-8') as f:
        js_content = f.read()
    
    # 检查算法优化
    optimizations = {
        "股票密度权重": "stockDensityWeight" in js_content,
        "基础高度减少": "baseHeight = 35" in js_content,
        "行高优化": "rowHeight = 20" in js_content,
        "基础分配增强": "stockRatio * 1.1" in js_content,
        "严格上限控制": "stockCount >= 30" in js_content and "0.65" in js_content
    }
    
    print("   JavaScript优化检查结果:")
    for optimization, status in optimizations.items():
        status_icon = "✅" if status else "❌"
        print(f"     {status_icon} {optimization}: {'已优化' if status else '未优化'}")
    
    return all(optimizations.values())

def generate_optimization_report():
    """生成优化报告"""
    print("\n📋 生成优化报告...")
    
    report = {
        "optimization_date": datetime.now().isoformat(),
        "version": "08",
        "optimizations": {
            "css_optimizations": {
                "overall_success_rate_compression": "减少整体胜率模块高度到22px",
                "padding_reduction": "减少内边距到3px 8px",
                "line_height_optimization": "统一行高为1.1",
                "level_row_height_reduction": "减少级别行最小高度到38px",
                "alignment_improvement": "优化垂直居中对齐"
            },
            "js_optimizations": {
                "stock_density_weighting": "基于股票数量的密度权重算法",
                "base_height_reduction": "减少基础高度到35px",
                "row_height_optimization": "优化行高到20px",
                "enhanced_base_allocation": "增强基础分配10%",
                "strict_upper_limits": "基于股票数量的严格上限控制"
            }
        },
        "expected_improvements": {
            "space_utilization": "提高空间利用率5-10%",
            "display_capacity": "增加股票显示数量15-25%",
            "visual_alignment": "改善图标和文字对齐效果",
            "user_experience": "提升整体视觉体验"
        }
    }
    
    report_file = f"optimization_report_08_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"   ✅ 优化报告已保存: {report_file}")
    return report_file

def main():
    """主函数"""
    print("🚀 连板金字塔空间优化验证 08")
    print("=" * 50)
    
    # 验证空间分配算法
    verify_space_allocation_algorithm()
    
    # 检查CSS优化
    css_ok = check_css_optimization()
    
    # 检查JavaScript优化
    js_ok = check_js_optimization()
    
    # 生成优化报告
    report_file = generate_optimization_report()
    
    print("\n" + "=" * 50)
    print("📊 验证总结:")
    print(f"   CSS优化: {'✅ 完成' if css_ok else '❌ 需要检查'}")
    print(f"   JavaScript优化: {'✅ 完成' if js_ok else '❌ 需要检查'}")
    print(f"   优化报告: ✅ 已生成 ({report_file})")
    
    if css_ok and js_ok:
        print("\n🎉 所有优化已完成！建议进行实际测试验证效果。")
        print("💡 测试建议:")
        print("   1. 打开 test_space_optimization_08.html 进行可视化测试")
        print("   2. 测试不同场景下的空间分配效果")
        print("   3. 验证图标对齐和整体胜率显示效果")
        print("   4. 检查在不同股票数量分布下的表现")
    else:
        print("\n⚠️  部分优化未完成，请检查相关文件。")

if __name__ == "__main__":
    main()
