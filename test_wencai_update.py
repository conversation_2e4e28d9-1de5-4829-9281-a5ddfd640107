#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的问财脚本
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_wencai_script():
    """测试更新后的问财脚本"""
    print("🧪 测试更新后的问财脚本")
    print("=" * 50)
    
    try:
        # 导入更新后的脚本
        from wencai_formatted import get_formatted_market_data, test_wencai_data_structure
        
        print("✅ 脚本导入成功")
        
        # 测试数据结构调试功能
        print("\n🔍 测试数据结构调试功能:")
        test_wencai_data_structure()
        
        print("\n" + "=" * 50)
        print("🚀 测试主要数据获取功能:")
        
        # 测试主要功能
        result = get_formatted_market_data()
        
        if result:
            print("\n✅ 数据获取测试成功!")
            print("📊 返回的数据结构:")
            for key, value in result.items():
                print(f"   {key}: {value}")
        else:
            print("\n❌ 数据获取测试失败")
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保所有依赖包已安装:")
        print("  - pywencai")
        print("  - pandas")
        print("  - market_info_sina (自定义模块)")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_wencai_script()
