# 问财脚本更新说明

## 更新概述

根据您的要求，已更新 `wencai_formatted.py` 脚本，实现以下功能：

1. **成交额数据**：继续从新浪财经获取
2. **其他所有数据**：统一从问财获取，使用一句话查询
3. **查询语句**：`res = pywencai.get(query='A股下跌家数',query_type='zhishu')`

## 主要变更

### 1. 新增数据解析函数

```python
def extract_market_data_from_wencai(data):
    """从问财返回的数据中提取所有市场信息"""
```

这个函数能够从问财返回的JSON数据中提取：
- 上涨家数
- 下跌家数  
- 平盘家数
- 涨停家数
- 跌停家数

### 2. 简化数据获取流程

原来的多次查询：
```python
# 旧版本 - 多次查询
up_data = pywencai.get(query='今天A股上涨家数', query_type='zhishu')
down_data = pywencai.get(query='今天A股下跌家数', query_type='zhishu')
```

现在的单次查询：
```python
# 新版本 - 单次查询获取所有数据
res = pywencai.get(query='A股下跌家数', query_type='zhishu')
market_data = extract_market_data_from_wencai(res)
```

### 3. 数据来源说明

- **涨跌家数、平盘家数、涨跌停数据**：问财(同花顺)
- **成交额数据**：新浪财经
- **涨跌停股票详情**：问财(同花顺)

### 4. 新增调试功能

添加了数据结构调试功能，可以查看问财返回的原始数据：

```bash
python wencai_formatted.py debug
```

## 使用方法

### 正常运行
```bash
python wencai_formatted.py
```

### 调试模式
```bash
python wencai_formatted.py debug
```

### 测试模式
```bash
python wencai_formatted.py test
```

## 数据解析策略

脚本采用多种策略解析问财数据：

1. **方法1**：从 `table2` 字段提取数据
2. **方法2**：从 `涨跌家数分布` 字段提取数据  
3. **方法3**：从文本字段使用正则表达式提取数据

这样可以适应问财不同的数据返回格式。

## 输出示例

```
🚀 问财A股市场数据获取
============================================================
📊 正在从问财获取市场数据...
✅ 问财数据获取成功
📈 上涨家数: 1234
📉 下跌家数: 2345
📊 平盘家数: 123
🎯 涨停家数: 45
🎯 跌停家数: 67
💰 正在从新浪财经获取总成交额...
   ✅ 从新浪财经获取成交额: 1.23万亿
```

## 错误处理

- 如果问财查询失败，会显示详细的错误信息和解决建议
- 如果新浪财经成交额获取失败，会继续执行其他功能
- 添加了调试信息输出，便于排查问题

## 测试

可以使用提供的测试脚本验证功能：

```bash
python test_wencai_update.py
```

## 注意事项

1. 确保已安装 `pywencai` 包
2. 确保 `market_info_sina.py` 模块可用
3. 网络连接正常，能够访问问财和新浪财经接口
4. 如果数据解析失败，可以使用调试模式查看原始数据结构
