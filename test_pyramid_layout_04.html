<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试程序04 - 连板金字塔布局修复验证</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="show_data/static/css/style.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .comparison-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .before-after {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
        }
        .before {
            border-color: #dc3545;
        }
        .after {
            border-color: #28a745;
        }
        .test-info {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin-bottom: 20px;
        }
        .layout-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .metric-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #007bff;
        }
        .metric-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-info">
            <h4>🔧 测试程序04 - 连板金字塔布局修复验证</h4>
            <p><strong>测试目标：</strong>验证连板金字塔模块的布局问题修复效果</p>
            <p><strong>修复内容：</strong></p>
            <ul>
                <li>减少空白空间：padding从10px减少到5px，margin优化</li>
                <li>标签对齐：统一左右标签的垂直对齐方式为center</li>
                <li>整体胜率对齐：优化整体胜率显示的对齐方式</li>
                <li>移动端适配：同步调整移动端样式</li>
            </ul>
        </div>

        <!-- 布局指标对比 -->
        <div class="test-card">
            <h5>📊 布局指标对比</h5>
            <div class="layout-metrics">
                <div class="metric-card">
                    <div class="metric-value" id="padding-reduction">50%</div>
                    <div class="metric-label">内边距减少</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="margin-reduction">50%</div>
                    <div class="metric-label">外边距减少</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="height-reduction">8%</div>
                    <div class="metric-label">最小高度减少</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="alignment-improvement">100%</div>
                    <div class="metric-label">对齐改善</div>
                </div>
            </div>
        </div>

        <!-- 金字塔显示区域 -->
        <div class="test-card">
            <h5>🏗️ 连板金字塔显示（修复后）</h5>
            <div class="pyramid-card-new" style="height: 500px;">
                <div class="card-header py-2 text-center border-0 bg-gradient-to-r from-blue-50 to-purple-50">
                    <h6 class="mb-0 font-semibold text-gray-800">连板金字塔</h6>
                    <small class="text-gray-500" id="pyramid-date">加载中...</small>
                </div>
                <div class="card-body p-3">
                    <div class="pyramid-layout-fullwidth">
                        <div id="lianban-pyramid-visual" class="pyramid-container-fullwidth">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2 text-muted">正在加载连板数据...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试结果 -->
        <div class="test-card">
            <h5>✅ 测试结果</h5>
            <div id="test-results">
                <div class="alert alert-info">
                    <strong>测试进行中...</strong> 正在验证布局修复效果
                </div>
            </div>
        </div>

        <!-- 布局检查工具 -->
        <div class="test-card">
            <h5>🔍 布局检查工具</h5>
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-primary w-100 mb-2" onclick="checkSpacing()">检查空白空间</button>
                    <button class="btn btn-success w-100 mb-2" onclick="checkAlignment()">检查标签对齐</button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-warning w-100 mb-2" onclick="checkOverallRate()">检查整体胜率对齐</button>
                    <button class="btn btn-info w-100 mb-2" onclick="checkMobileLayout()">检查移动端布局</button>
                </div>
            </div>
            <div id="check-results" class="mt-3"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="show_data/static/js/charts.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 测试程序04启动 - 连板金字塔布局修复验证');
            
            // 加载连板数据
            loadLianbanData();
            
            // 延迟检查布局
            setTimeout(checkLayoutImprovements, 2000);
        });

        // 检查布局改进效果
        function checkLayoutImprovements() {
            const results = [];
            
            // 检查容器padding
            const textDisplay = document.querySelector('.lianban-text-display');
            if (textDisplay) {
                const padding = window.getComputedStyle(textDisplay).padding;
                results.push({
                    test: '容器内边距',
                    expected: '5px',
                    actual: padding,
                    passed: padding.includes('5px')
                });
            }
            
            // 检查整体胜率对齐
            const overallRate = document.querySelector('.overall-success-rate');
            if (overallRate) {
                const display = window.getComputedStyle(overallRate).display;
                const alignItems = window.getComputedStyle(overallRate).alignItems;
                results.push({
                    test: '整体胜率对齐',
                    expected: 'flex + center',
                    actual: `${display} + ${alignItems}`,
                    passed: display === 'flex' && alignItems === 'center'
                });
            }
            
            // 检查级别行间距
            const levelRows = document.querySelectorAll('.lianban-level-row');
            if (levelRows.length > 0) {
                const marginBottom = window.getComputedStyle(levelRows[0]).marginBottom;
                results.push({
                    test: '级别行间距',
                    expected: '1px',
                    actual: marginBottom,
                    passed: marginBottom === '1px'
                });
            }
            
            // 检查标签对齐
            const levelStats = document.querySelector('.level-stats');
            if (levelStats) {
                const alignItems = window.getComputedStyle(levelStats).alignItems;
                results.push({
                    test: '标签对齐',
                    expected: 'center',
                    actual: alignItems,
                    passed: alignItems === 'center'
                });
            }
            
            displayTestResults(results);
        }

        // 显示测试结果
        function displayTestResults(results) {
            const container = document.getElementById('test-results');
            const passedTests = results.filter(r => r.passed).length;
            const totalTests = results.length;
            
            let html = `
                <div class="alert ${passedTests === totalTests ? 'alert-success' : 'alert-warning'}">
                    <strong>测试完成：</strong> ${passedTests}/${totalTests} 项测试通过
                </div>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>测试项目</th>
                                <th>期望值</th>
                                <th>实际值</th>
                                <th>结果</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            results.forEach(result => {
                html += `
                    <tr class="${result.passed ? 'table-success' : 'table-danger'}">
                        <td>${result.test}</td>
                        <td>${result.expected}</td>
                        <td>${result.actual}</td>
                        <td>${result.passed ? '✅ 通过' : '❌ 失败'}</td>
                    </tr>
                `;
            });
            
            html += '</tbody></table></div>';
            container.innerHTML = html;
        }

        // 检查空白空间
        function checkSpacing() {
            const results = [];
            const elements = [
                { selector: '.lianban-text-display', property: 'padding', expected: '5px' },
                { selector: '.overall-success-rate', property: 'padding', expected: '2px 6px' },
                { selector: '.lianban-level-row', property: 'margin-bottom', expected: '1px' }
            ];
            
            elements.forEach(({ selector, property, expected }) => {
                const element = document.querySelector(selector);
                if (element) {
                    const value = window.getComputedStyle(element)[property];
                    results.push(`${selector} ${property}: ${value} (期望: ${expected})`);
                }
            });
            
            document.getElementById('check-results').innerHTML = 
                '<div class="alert alert-info"><strong>空白空间检查：</strong><br>' + 
                results.join('<br>') + '</div>';
        }

        // 检查标签对齐
        function checkAlignment() {
            const results = [];
            const alignmentElements = [
                { selector: '.level-info', property: 'align-items' },
                { selector: '.level-stats', property: 'align-items' },
                { selector: '.lianban-level-row', property: 'align-items' }
            ];
            
            alignmentElements.forEach(({ selector, property }) => {
                const element = document.querySelector(selector);
                if (element) {
                    const value = window.getComputedStyle(element)[property];
                    results.push(`${selector} ${property}: ${value}`);
                }
            });
            
            document.getElementById('check-results').innerHTML = 
                '<div class="alert alert-success"><strong>标签对齐检查：</strong><br>' + 
                results.join('<br>') + '</div>';
        }

        // 检查整体胜率对齐
        function checkOverallRate() {
            const element = document.querySelector('.overall-success-rate');
            if (element) {
                const styles = window.getComputedStyle(element);
                const results = [
                    `display: ${styles.display}`,
                    `align-items: ${styles.alignItems}`,
                    `justify-content: ${styles.justifyContent}`,
                    `text-align: ${styles.textAlign}`
                ];
                
                document.getElementById('check-results').innerHTML = 
                    '<div class="alert alert-warning"><strong>整体胜率对齐检查：</strong><br>' + 
                    results.join('<br>') + '</div>';
            }
        }

        // 检查移动端布局
        function checkMobileLayout() {
            const isMobile = window.innerWidth <= 768;
            const results = [`当前屏幕宽度: ${window.innerWidth}px (${isMobile ? '移动端' : '桌面端'})`];
            
            if (isMobile) {
                const mobileElements = [
                    '.pyramid-container-new',
                    '.pyramid-layout-fullwidth'
                ];
                
                mobileElements.forEach(selector => {
                    const element = document.querySelector(selector);
                    if (element) {
                        const styles = window.getComputedStyle(element);
                        results.push(`${selector} height: ${styles.height}, padding: ${styles.padding}`);
                    }
                });
            }
            
            document.getElementById('check-results').innerHTML = 
                '<div class="alert alert-info"><strong>移动端布局检查：</strong><br>' + 
                results.join('<br>') + '</div>';
        }
    </script>
</body>
</html>
